"use client";
import React, { useEffect, useState } from "react";
import Image from "next/image";
import Modal from "react-modal";

import {
  apiCall,
  getLookUpData,
  getOapDetail,
  getOapForm,
  getOapFormSections,
  saveOapForm,
} from "@/api/api";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { useFormContext } from "react-hook-form";
import DynamicFields from "../custom/DynamicFields";
import { useRouter } from "next/navigation";
import loader from "../../public/loader.svg";
import loader2 from "../../public/loader2.svg";
import ReactMarkdown from "react-markdown";
import { useAtom } from "jotai";
import {
  applicationId,
  consumerAPIKey,
  email,
  nextForm,
  preferredLanguage,
  qualifyingQuestions,
} from "@/lib/atom";
import { LayoutWrapper } from "../custom/wrapper";
import { LinkRenderer } from "../custom/linkRender";
import { QuestionnairePopup } from "../custom/questionnaire-popup";
import remarkGfm from "remark-gfm";
import rehypeRaw from "rehype-raw";
import { useProgress } from "@/lib/progress-context";
import LanguageSelector from "../custom/translate";
import { fontSizeAtom } from "@/lib/atom";
import { getBrandSpecificFontStyle } from "@/lib/brandUtils";

const ApplicationFilter = () => {
  const [applicationFields, setApplicationFields] = useState<any>({});
  const [dropdownOptions, setDropdownOptions] = useState<any>({});
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [saving, setSaving] = useState<boolean>(false);
  const [apiKey] = useAtom(consumerAPIKey);
  const [modalIsOpen, setIsOpen] = useState<boolean>(false);
  const [programDetails, setProgramDetails] = useState<any>({});
  const [qualifyingQuestionsState] = useAtom<any>(qualifyingQuestions);
  const [preferLang] = useAtom(preferredLanguage);

  const queryClient = useQueryClient();
  const router = useRouter();
  const [applicationDetails, setApplicationDetails] = useState<any>(null);
  const { setProgress } = useProgress();
  const [nextFormDetails, setNextFormDetails]: any = useAtom(nextForm);
  const [userEmail, setUserEmail] = useAtom(email);
  const [application, setApplication] = useAtom(applicationId);
  const [errors, setErrors]: any = useState({});
  const { register, setValue, getValues } = useFormContext();
  const [fontSize] = useAtom(fontSizeAtom);

  const [showQuestionarrieModal, setShowQuestionarrieModal] =
    useState<boolean>(false);

  const [pageDetails, setPageDetails] = useState({
    screen: process.env.NEXT_PUBLIC_OAP_NAME,
    mode: process.env.NEXT_PUBLIC_OAP_MODE,
  });

  const { data: labels } = useQuery({
    queryKey: ["oap/lookup/programmes"],
    queryFn: async () => {
      let res = await getLookUpData(
        {
          name: "oap/lookup/programmes",
        },
        apiKey,
        {
          ...(preferLang && { displayLanguage: preferLang }),
        }
      );

      return res;
    },
    enabled: !!apiKey,
  });

  const {
    data: sectionQuery,
    refetch: refetchSectionQuery,
    isFetching: sectionIsFetching,
  } = useQuery({
    queryKey: [
      `${nextFormDetails?.oap}-${nextFormDetails?.mode}-${nextFormDetails?.form}`,
    ],
    queryFn: async () => {
      if (
        nextFormDetails?.form &&
        nextFormDetails?.oap &&
        nextFormDetails?.mode
      ) {
        let res = await getOapForm(
          {
            oap: nextFormDetails?.oap,
            form: nextFormDetails?.form,
            mode: nextFormDetails?.mode,
            ...(preferLang === "de" && { language: "de" }),
          },
          apiKey
        );
        return res;
      } else {
        // router.push("/login");
        return;
      }
    },
    enabled: !!nextFormDetails,
  });
  const handlePreQualificationResubmit = (
    e: React.MouseEvent<HTMLAnchorElement, MouseEvent>
  ) => {
    e.preventDefault();
    router.push("/app-prequalifying-questions");
    return;
  };
  const {
    data: formQuery,
    refetch: refetchFormQuery,
    isFetching: formQueryIsFetching,
  } = useQuery({
    // next sections page
    queryKey: [
      `${nextFormDetails?.oap}-
    ${nextFormDetails?.mode}-${sectionQuery?.form}`,
    ],
    queryFn: async () => {
      if (!(sectionQuery?.section?.[0]?.name && sectionQuery?.form)) return;
      let res = await getOapFormSections(
        {
          oap: nextFormDetails?.oap,
          mode: nextFormDetails?.mode,
          formName: sectionQuery?.form,
          sectionName: sectionQuery?.section?.[0]?.name,
          ...(preferLang === "de" && { language: "de" }),
        },
        apiKey
      );

      return res;
    },
    enabled: !!(nextFormDetails && sectionQuery),
  });

  const { data: questionariesSectionQuery } = useQuery({
    queryKey: ["questionnaire-section", application],
    queryFn: async () => {
      let res = await getOapFormSections(
        {
          oap: process.env.NEXT_PUBLIC_OAP_NAME,
          mode: process.env.NEXT_PUBLIC_OAP_MODE,
          formName: "PROGRAM_FILTER",
          sectionName: "QUESTIONNAIRE_POPUP",
        },
        apiKey
      );

      return res;
    },
    enabled:
      !!apiKey &&
      formQuery?.fieldData?.filter((item: any) => item.type == "button")[0]
        ?.canHaveQuestionnarieFields,
  });

  const object = formQuery?.fieldData?.[0]?.popupDetails?.fieldData
    ? formQuery?.fieldData?.[0]?.popupDetails?.fieldData
    : formQuery?.fieldData;

  useQuery({
    queryKey: [`next-form-screen`],
    queryFn: async () => {
      let tempObj = {
        email: nextFormDetails.email,
        applicationId: application,
        ...(preferLang === "de" && { language: "de" }),
      };

      object?.forEach((ele: any) => {
        if (ele.action == "nextForm") {
          tempObj = { ...tempObj, ...ele[ele.action] };
        }
      });
      return tempObj;
    },
    enabled: object?.length > 0,
  });

  function flattenPayload(payload: any) {
    const flattened: any = {};
    for (const key in payload) {
      if (Object.prototype.hasOwnProperty.call(payload, key)) {
        if (typeof payload[key] === "object" && payload[key] !== null) {
          flattened[key] = payload[key].value;
        } else {
          flattened[key] = payload[key];
        }
      }
    }
    return flattened;
  }

  const getNextForm = () => {
    const formDetail = object?.find(
      (item: any) => item?.type === "button"
    )?.nextForm;
    return formDetail;
  };
  const getLocationForm = () => {
    const formDetail = object?.find(
      (item: any) => item?.type === "secondary-button"
    )?.nextForm;
    return formDetail;
  };

  const transformOptions = (options: any[]) => {
    if (!options || !Array.isArray(options)) return [];

    return options.map((option) => {
      const displayText = option.displayText || option.label;
      return {
        ...option,
        displayText,
      };
    });
  };

  useEffect(() => {
    refetchSectionQuery();
    refetchFormQuery();
  }, [preferLang]);

  // Prepopulate form fields with values from previous screen
  useEffect(() => {
    if (formQuery?.fieldData && Object.keys(applicationFields).length === 0) {
      const existingValues = getValues();
      console.log("Prepopulating with existing values:", existingValues);

      const prepopulatedFields: any = {};

      // First pass: collect all prepopulated values
      formQuery.fieldData.forEach((field: any) => {
        const fieldName = field.fieldName;
        const fieldDisplayName =
          field.fieldDisplayName || `${fieldName}DisplayName`;

        // Check if this field has a value from previous screen
        if (existingValues[fieldName]) {
          prepopulatedFields[fieldName] = existingValues[fieldName];

          // Also set the display name if it exists
          if (existingValues[fieldDisplayName]) {
            prepopulatedFields[fieldDisplayName] =
              existingValues[fieldDisplayName];
          }

          console.log(`Prepopulated ${fieldName}:`, existingValues[fieldName]);
        }
      });

      // Update applicationFields state with prepopulated values
      if (Object.keys(prepopulatedFields).length > 0) {
        setApplicationFields(prepopulatedFields);
        console.log("ApplicationFields prepopulated:", prepopulatedFields);

        // Second pass: trigger dependent field updates for prepopulated fields
        setTimeout(() => {
          // Find the first field that has a value and child field to trigger dependent loading
          const fieldWithDependency = formQuery.fieldData.find(
            (field: any) => prepopulatedFields[field.fieldName]
          );

          if (fieldWithDependency) {
            const fieldName = fieldWithDependency.fieldName;
            const fieldIndex = formQuery.fieldData.findIndex(
              (f: any) => f.fieldName === fieldName
            );

            console.log(`Triggering dependent field update for ${fieldName}`);

            // Set the item to the next field to trigger the dependent field loading
            const nextItem = formQuery.fieldData[fieldIndex + 1];
            if (nextItem) {
              setApplicationFields((prev: any) => ({
                ...prev,
                item: nextItem,
                level: prepopulatedFields[fieldName], // This will trigger the useEffect
              }));
            }
          }
        }, 200); // Small delay to ensure state is updated
      }
    }
  }, [formQuery?.fieldData]);

  function validateOutput(outputObject: any, fieldData: any) {
    let temp: any[] = [];
    fieldData?.forEach((field: any) => {
      const { fieldName, required, rules, visibleWhen } = field;
      if (checkVisibility(visibleWhen)) {
        if (Object.keys(outputObject).length == 0) {
          temp.push(rules?.required);

          if (rules) {
            setErrors((prev: any) => ({
              ...prev,
              [fieldName]: rules?.required,
            }));
          }
        } else {
          const fieldValue = outputObject[fieldName];
          if (!fieldName) {
          } else if (required && !(fieldName in outputObject)) {
            temp.push(rules?.required);
            setErrors((prev: any) => ({
              ...prev,
              [fieldName]: rules?.required,
            }));
          } else if (fieldValue == undefined || fieldValue == null) {
            temp.push(rules?.required);
            setErrors((prev: any) => ({
              ...prev,
              [fieldName]: rules?.required,
            }));
          }
        }
      }
    });

    return temp;
  }

  const handleCancelationApplication = (questionnaireFields?: any) => {
    (async () => {
      let temp = flattenPayload(applicationFields);

      const basicDetails = JSON.parse(
        localStorage.getItem("basic-details") ?? "[]"
      );

      let isValid = validateOutput(temp, object);

      if (isValid.length !== 0) {
        setSaving((prev: boolean) => !prev);
        return;
      }

      let res: any = {};

      if (Object.keys(programDetails).length === 0) {
        res = await saveOapForm(
          {
            email: userEmail,
            applicationId: application,
            applicationStatus: "inProgress",
            ...temp,
            ...basicDetails,
            ...questionnaireFields,
            sectionLabel: formQuery?.label,
          },
          { oapName: nextFormDetails?.oap, mode: nextFormDetails?.mode },
          apiKey
        );
      } else {
        res = await saveOapForm(
          {
            email: userEmail,
            applicationId: application,
            applicationStatus: "inProgress",
            ...programDetails,
            ...temp,
            ...basicDetails,
            ...questionnaireFields,
            sectionLabel: formQuery?.label,
          },
          { oapName: nextFormDetails?.oap, mode: nextFormDetails?.mode },
          apiKey
        );
      }
    })();
  };

  const getPopupDetails = (formQuery: any) => {
    // Case 1: Popup details at root level

    if (formQuery?.popupDetails?.fieldData) {
      return formQuery?.popupDetails?.fieldData;
    }

    // Case 2: Popup details inside fieldData[0]

    if (formQuery?.fieldData?.[0]?.popupDetails?.fieldData) {
      return formQuery?.fieldData?.[0]?.popupDetails?.fieldData;
    }

    // Case 3: Popup details inside programCards field

    const programCardsField = formQuery?.fieldData?.find(
      (field: any) => field.type === "programCards"
    );

    if (programCardsField?.popupDetails?.fieldData) {
      return programCardsField?.popupDetails?.fieldData;
    }
    return null;
  };

  const handleSaveApplication = (questionnaireFields?: any) => {
    setSaving((prev: boolean) => !prev);

    (async () => {
      let temp = flattenPayload(applicationFields);

      const basicDetails = JSON.parse(
        localStorage.getItem("basic-details") ?? "[]"
      );

      let isValid = validateOutput(temp, object);

      if (isValid.length !== 0) {
        setSaving(() => false);
        return;
      }

      if (formQuery?.canHavePathwayPartners && !modalIsOpen) {
        setIsOpen(true);
        setSaving((prev: boolean) => false);
        return;
      }

      let res: any = {};

      let basePayload = {
        email: userEmail,
        applicationStatus: "inProgress",
        ...temp,
        ...basicDetails,
        ...questionnaireFields,
        sectionLabel: formQuery?.label,
      };

      if (Object.keys(programDetails).length > 0) {
        basePayload = {
          ...basePayload,
          ...programDetails,
        };
      }
      if (Object.keys(questionnaireFields || {}).length > 0) {
        basePayload = {
          ...basePayload,
          ...questionnaireFields,
        };
      }

      if (Object.keys(applicationDetails || {}).length > 0) {
        basePayload = {
          ...basePayload,
          ...applicationDetails,
        };
        setApplicationDetails({});
      }

      if (Object.keys(qualifyingQuestionsState).length > 0) {
        basePayload = {
          ...basePayload,
          ...qualifyingQuestionsState,
        };
      }

      res = await saveOapForm(
        basePayload,
        { oapName: nextFormDetails?.oap, mode: nextFormDetails?.mode },
        apiKey
      );

      await queryClient.setQueryData(["next-form-screen"], () => {
        let tempObj = {};
        object?.forEach((ele: any) => {
          if (ele.action == "nextForm") {
            tempObj = {
              email: res?.email,
              applicationId: res?.applicationId,
              ...ele[ele.action],
            };
          }
        });
        return tempObj;
      });

      if (res?.email && res?.applicationId) {
        let nextForm = await getNextForm();
        let nextApply = basicDetails?.[nextForm?.dependentField];

        setNextFormDetails(getNextForm());
        setUserEmail(res?.email);
        setApplication(res?.applicationId);
        localStorage.removeItem("basic-details");
        setProgress(res?.progress);
        setValue("ocrReprocessCount", 0);

        if (nextForm?.type == "multiple") {
          router.push(`/form?apply=${nextApply}&step=0`);
          return;
        }

        if (nextFormDetails?.type == "single") {
          router.push(`/form?apply=${getNextForm()?.form}&step=0`);
          return;
        }
        router.push(
          `/form?apply=${applicationFields?.applicationType?.label}&step=0`
        );
        return;
      }
    })();
  };

  const handleSaveApplication2 = (
    label: any,
    value: any,
    level: any,
    levelId: any
  ) => {
    (async () => {
      let temp2 = flattenPayload(applicationFields);
      const basicDetails2 = JSON.parse(
        localStorage.getItem("basic-details") ?? "[]"
      );

      const fieldDisplayName = formQuery?.fieldData?.[0]?.fieldDisplayName;
      const fieldName = formQuery?.fieldData?.[0]?.fieldName;

      const res = await saveOapForm(
        {
          email: userEmail,
          applicationId: application,
          [fieldDisplayName]: label,
          [fieldName]: value,
          levelDisplayName: level,
          level: levelId,
          applicationStatus: "inProgress",
          applicationType: level,
          ...temp2,
          ...basicDetails2,
          sectionLabel: formQuery?.label,
        },
        { oapName: nextFormDetails?.oap, mode: nextFormDetails?.mode },
        apiKey
      );

      if (Object.keys(temp2).length !== 0) {
        let isValid = validateOutput(temp2, object);
        if (isValid.length !== 0) {
          setSaving((prev: boolean) => false);
          return;
        }
      }
    })();
  };

  useEffect(() => {
    (async () => {
      let item: any = {};

      if (formQuery?.canHavePathwayPartners) {
        item = getPopupDetails(formQuery).find(
          (ele: any) => ele.type === "pickList"
        );
      }
      let url = "oap/lookup";
      if (item?.picklistSourceType == "Inline") {
        let picklistValues = item.pickListValues;
        setDropdownOptions((prev: any) => ({
          ...prev,
          [item?.fieldName]: picklistValues,
        }));
      } else {
        if (item?.picklistSource == undefined) return;
        if (item?.picklistSource?.includes("${")) {
          let originalString = item?.picklistSource;
          const regex = /\${(.*?)}/g;
          const replacedString = originalString.replace(
            regex,
            (match: string, group: string) => {
              if (!isNaN(applicationFields[group]?.value)) {
                // checking number values
                return Number(applicationFields[group]?.value);
              }
              return applicationFields[group]?.value || match;
            }
          );
          setIsLoading(true);
          let data = await apiCall(
            `${replacedString}`,
            {
              method: "GET",
              key: apiKey,
            },
            {
              ...(preferLang && { displayLanguage: preferLang }),
            }
          );
          setIsLoading(false);
          setDropdownOptions((prev: any) => ({
            ...prev,
            [item?.fieldName]: data,
          }));
        } else {
          let data = await apiCall(
            `${item?.picklistSource}`,
            {
              method: "GET",
              key: apiKey,
            },
            {
              ...(preferLang && { displayLanguage: preferLang }),
            }
          );
          setDropdownOptions((prev: any) => ({
            ...prev,
            [item?.fieldName]: data,
          }));
        }
      }
    })();
  }, [applicationFields.level, formQuery, applicationFields]);

  useEffect(() => {
    (async () => {
      let item =
        Object.keys(applicationFields).length != 0
          ? applicationFields.item
          : formQuery?.fieldData[0];
      if (item?.picklistSourceType == "Inline") {
        // Transform options to add displayText
        const transformedOptions = transformOptions(item.pickListValues);
        setDropdownOptions((prev: any) => ({
          ...prev,
          [item?.fieldName]: transformedOptions,
        }));
      } else {
        if (item?.picklistSource == undefined) return;
        if (item?.picklistSource?.includes("${")) {
          let originalString = item?.picklistSource;
          const regex = /\${(.*?)}/g;
          const replacedString = originalString.replace(
            regex,
            (match: string, group: string) => {
              const applicationDetails = getValues();
              console.log("App details ->", applicationDetails);
              if (!isNaN(applicationFields[group]?.value)) {
                // checking number values
                return Number(applicationFields[group]?.value);
              }
              return (
                applicationFields[group]?.value ||
                applicationFields[group] ||
                applicationDetails[group]?.value ||
                applicationDetails[group] ||
                match
              );
            }
          );
          setIsLoading(true);
          const data = await apiCall(
            `${replacedString}`,
            {
              method: "GET",
              key: apiKey,
            },
            {
              ...(preferLang && { displayLanguage: preferLang }),
            }
          );
          // Transform API response data
          const transformedData = transformOptions(data);
          setIsLoading(false);
          setDropdownOptions((prev: any) => ({
            ...prev,
            [item?.fieldName]: transformedData,
          }));
        } else {
          const data = await apiCall(
            `${item?.picklistSource}`,
            {
              method: "GET",
              key: apiKey,
            },
            {
              ...(preferLang && { displayLanguage: preferLang }),
            }
          );
          // Transform API response data
          const transformedData = transformOptions(data);
          setDropdownOptions((prev: any) => ({
            ...prev,
            [item?.fieldName]: transformedData,
          }));
        }
      }
    })();
  }, [applicationFields.level, formQuery, applicationFields]);

  const checkVisibility = (visibleWhenProps: any) => {
    if (!visibleWhenProps) {
      return true;
    }

    // Handle new "or" logic with multiple conditions
    if (visibleWhenProps.logic === "or" && Array.isArray(visibleWhenProps.conditions)) {
      return visibleWhenProps.conditions.some((condition: any) => {
        const fieldWatchValue =
          applicationFields[condition.fieldName] ||
          qualifyingQuestionsState[condition.fieldName];

        if (typeof fieldWatchValue === "object" && fieldWatchValue !== null && !Array.isArray(fieldWatchValue)) {
          return fieldWatchValue?.value === condition.value || fieldWatchValue?.label === condition.value;
        }

        if (Array.isArray(fieldWatchValue)) {
          return fieldWatchValue.some(
            (v: any) => v?.value === condition.value || v?.label === condition.value || v === condition.value
          );
        }

        return fieldWatchValue === condition.value;
      });
    }

    // Handle existing single condition logic
    const fieldWatchValue =
      applicationFields[visibleWhenProps.fieldName] ||
      qualifyingQuestionsState[visibleWhenProps.fieldName];
    // Check if visibleWhenProps.value is an array
    const valueArr = Array.isArray(visibleWhenProps.value)
      ? visibleWhenProps.value
      : [visibleWhenProps.value];

    // Check if any value in the valueArr matches fieldWatchValue
    if (
      valueArr.some((value: any) => value === fieldWatchValue?.label) ||
      valueArr.some((value: any) => value === fieldWatchValue) ||
      valueArr.some((value: any) => value === fieldWatchValue?.value)
    ) {
      return true;
    }

    return false;
  };
  function closeModal() {
    setIsOpen(false);
    // setApplicationFields({});
  }

  if (sectionIsFetching || formQueryIsFetching || saving) {
    return (
      <main className="min-h-screen bg-on-background flex flex-col items-center justify-center overflow-scroll w-full">
        <Image
          priority
          src={loader}
          height={32}
          width={32}
          alt="Follow us on Twitter"
        />
      </main>
    );
  }

  return (
    <>
      {formQuery && (
        <div className="w-full">
          <div className="w-full">
            <header className="p-6 header bg-on-background flex justify-between items-center w-full max-h-[98px]">
              <div className="flex items-center gap-4 flex-1">
                <div className="header-brand">
                  <a className="header-brand-link">
                    <Image
                      className="header-brand-image"
                      src={sectionQuery?.logoInfo?.signedUrl}
                      alt="ucw_logo_int"
                      width={sectionQuery?.logoInfo?.width}
                      height={sectionQuery?.logoInfo?.height}
                    />
                  </a>
                </div>
                <div className="header-content text-highlight text-xl font-normal items-center sm:flex hidden">
                  <div className="header-title header-title_general border-l border-[#] pl-[34px]">
                    <div className="header-title-value">
                      {sectionQuery?.headerTitle}
                    </div>
                  </div>
                </div>
              </div>

              {sectionQuery?.languageData &&
                sectionQuery?.languageData.canShowLanguageSwitch && (
                  <div className="ml-auto">
                    <LanguageSelector
                      languageData={sectionQuery?.languageData}
                    />
                  </div>
                )}
            </header>
            <div className=" lg:px-12 h-full lg-mb-12">
              <LayoutWrapper>
                <div className="prose dark:prose-invert max-w-none mb-4">
                  <ReactMarkdown
                    className="markDown m-4"
                    components={{ a: LinkRenderer }}
                    remarkPlugins={[remarkGfm]}
                    rehypePlugins={[rehypeRaw]}
                  >
                    {sectionQuery?.description}
                  </ReactMarkdown>
                </div>
                <div className="index-widget shadow-[0_3px_10px_rgb(0,0,0,0.2)] rounded p-[30px] lg:mb-[50px] sm:mb-0 md:mb-0">
                  <div className="index-widget-wrapper">
                    <form className="form" noValidate>
                      <div
                        className={`form-section w-full flex flex-col items-center gap-4`}
                      >
                        <h3 className="form-section-title text-primary font-semibold mb-2 text-xl">
                          {formQuery?.displayName}
                        </h3>
                        {formQuery.preQualificationResubmit && (
                          <div className="p-6 mb-4 bg-gray-100 flex justify-center w-[95%] text-center rounded-md">
                            <ReactMarkdown
                              className="markDown pl-9 pr-9"
                              components={{
                                a: ({ href, children }) => (
                                  <a
                                    href={href}
                                    className="text-[#01B0E1] underline"
                                    onClick={handlePreQualificationResubmit}
                                  >
                                    {children}
                                  </a>
                                ),
                              }}
                            >
                              {formQuery.preQualificationResubmit.text}
                            </ReactMarkdown>
                          </div>
                        )}
                        <div className=" form-section-content justify-center flex flex-col lg:flex-row flex-wrap gap-4 items-center w-full font-thin h--28 ">
                          {formQuery?.fieldData?.map((item: any, i: number) => {
                            if (item?.type === "programCards") {
                              return (
                                <div
                                  className="bg-background w-full flex flex-wrap gap-4"
                                  key={i}
                                >
                                  {labels?.map((data: any, index: any) => (
                                    <div
                                      key={index}
                                      style={{
                                        backgroundColor: "#f2f2f2",
                                        backgroundImage: `url(${formQuery?.layout?.backgroundImage})`,
                                        backgroundSize: "auto",
                                        backgroundRepeat: "no-repeat",
                                        backgroundPosition: "right center",
                                      }}
                                      className="border border-gray-300 p-4 rounded-lg flex flex-col lg:flex-row items-center w-full lg:w-[47%] justify-between"
                                    >
                                      <p className="text-lg font-bold mb-4 lg:mb-0">
                                        {data?.label}
                                      </p>
                                      <button
                                        className="button button_primary bg-[#212a33] hover:bg-secondary flex-shrink-0   text-white font-bold h-10 rounded cursor-pointer w-full lg:w-[150px] p-2 flex justify-center items-center"
                                        onClick={(e) => {
                                          e.preventDefault();
                                          setProgramDetails({
                                            programDisplayName: data?.label,
                                            program: data?.value,
                                            levelDisplayName: data?.level,
                                            level: data?.levelId,
                                          });

                                          handleSaveApplication2(
                                            data?.label,
                                            data?.value,
                                            data?.level,
                                            data?.levelId
                                          );
                                          setIsOpen(true);
                                        }}
                                      >
                                        {item?.placeHolder}
                                      </button>
                                    </div>
                                  ))}
                                </div>
                              );
                            }

                            if (!item?.fieldName && item.visibility)
                              return null;

                            return (
                              item.fieldName && (
                                <div
                                  className={`w-full max-h-96 ${
                                    item?.type !== "programCards"
                                      ? "lg:w-[28%]"
                                      : ""
                                  }`}
                                  key={i}
                                >
                                  <DynamicFields
                                    arrayIndex={i}
                                    getLookupData={
                                      dropdownOptions?.[item?.fieldName] || []
                                    }
                                    watch={() => {}}
                                    fromApplicationFilter={true}
                                    register={register}
                                    fieldItem={item}
                                    fieldType={item?.type}
                                    fieldName={item?.fieldName}
                                    isMandatory={item?.required}
                                    label={item?.displayName}
                                    isVisibleWhen={!item?.visibleWhen}
                                    selectedValue={
                                      applicationFields?.[item?.fieldName] ||
                                      getValues()[item?.fieldName] ||
                                      null
                                    }
                                    handleValueChanged={async (
                                      value: any,
                                      type?: string
                                    ) => {
                                      const {
                                        value: val,
                                        label,
                                        ...rest
                                      } = value;
                                      setErrors({});
                                      setApplicationFields((prev: any) => {
                                        let tempObj = {
                                          [item?.fieldName]: value,
                                          [item?.fieldDisplayName]: value.label,
                                          item: object?.[i + 1],
                                        };
                                        if (item?.childField)
                                          tempObj[item?.childField] = null;
                                        return { ...prev, ...rest, ...tempObj };
                                      });

                                      let currentNode = item;
                                      while (currentNode?.childField) {
                                        object.forEach(
                                          (element: any, index: number) => {
                                            if (
                                              element?.fieldName ==
                                              currentNode?.childField
                                            ) {
                                              setApplicationFields(
                                                (prev: any) => {
                                                  let tempObj: any = {};
                                                  if (element?.fieldName)
                                                    tempObj[
                                                      element?.fieldName
                                                    ] = null;
                                                  return {
                                                    ...prev,
                                                    ...tempObj,
                                                  };
                                                }
                                              );
                                              currentNode = element;
                                              return;
                                            }
                                          }
                                        );
                                      }
                                      setDropdownOptions((prev: any) => {
                                        return {
                                          ...prev,
                                          [item?.childField]: null,
                                        };
                                      });
                                    }}
                                    errorMessage={errors[item?.fieldName]}
                                    trigger={() => setErrors({})}
                                    isLoading={isLoading}
                                    required
                                  />
                                </div>
                              )
                            );
                          })}
                        </div>
                        {formQuery?.fieldData
                          ?.filter((item: any) => item.visibleWhen)
                          .map((ele: any, i: number) => {
                            return checkVisibility(ele?.visibleWhen) ? (
                              <div
                                key={i}
                                className="mb-5 items-center lg:mx-16"
                              >
                                <div className="max-w-none mb-2 text-sm">
                                  <p>{ele?.text}</p>
                                </div>
                              </div>
                            ) : null;
                          })}
                      </div>
                      <div
                        className={
                          formQuery?.fieldData?.filter(
                            (item: any) => item.type == "secondary-button"
                          ).length
                            ? `form-action flex justify-center mx-auto gap-4 max-w-[500px] flex-wrap`
                            : `form-action w-full flex justify-center`
                        }
                      >
                        {formQuery?.fieldData
                          ?.filter(
                            (item: any) => item.type == "secondary-button"
                          )
                          .map((ele: any, i: number) => (
                            <div
                              key={i}
                              className={`button button_primary bg-[#F4F4F4] hover:bg-[#E0E0E0] w-full mx-auto flex justify-center items-center font-bold h-[41px] rounded cursor-pointer ${
                                formQuery?.fieldData?.filter(
                                  (item: any) => item.type == "secondary-button"
                                ).length
                                  ? "flex-1"
                                  : ""
                              }`}
                              onClick={() => {
                                setNextFormDetails(getLocationForm());
                                router.push("/location");
                                return;
                              }}
                            >
                              {saving ? (
                                <div className=" w-full  flex items-center justify-center">
                                  <Image
                                    priority
                                    src={loader2}
                                    height={20}
                                    width={20}
                                    alt="Follow us on Twitter"
                                  />
                                </div>
                              ) : (
                                ele.placeholder
                              )}
                            </div>
                          ))}
                        {formQuery?.fieldData
                          ?.filter((item: any) => item.type == "button")
                          .map((ele: any, i: number) => (
                            <div
                              key={i}
                              className={`button button_primary bg-secondary hover:bg-primary w-full lg:w-[260px] mx-auto flex justify-center items-center text-white font-bold h-[41px] rounded cursor-pointer ${
                                formQuery?.fieldData?.filter(
                                  (item: any) => item.type == "secondary-button"
                                ).length
                                  ? "flex-1"
                                  : ""
                              }`}
                              style={getBrandSpecificFontStyle(
                                fontSize,
                                "label"
                              )}
                              onClick={() => {
                                const basicDetails: any = JSON.parse(
                                  localStorage.getItem("basic-details") as any
                                );
                                if (
                                  ele?.canHaveQuestionnarieFields &&
                                  questionariesSectionQuery?.allowedCountries?.includes(
                                    basicDetails[
                                      questionariesSectionQuery
                                        ?.allowedFieldName
                                    ]
                                  )
                                ) {
                                  let temp = flattenPayload(applicationFields);

                                  let isValid = validateOutput(temp, object);

                                  if (isValid.length === 0) {
                                    setShowQuestionarrieModal(true);
                                  }
                                } else {
                                  setApplicationFields((prev: any) => {
                                    return {
                                      ...prev,
                                    };
                                  });

                                  handleSaveApplication();
                                }
                              }}
                            >
                              {saving ? (
                                <div className=" w-full  flex items-center justify-center">
                                  <Image
                                    priority
                                    src={loader2}
                                    height={20}
                                    width={20}
                                    alt="Follow us on Twitter"
                                  />
                                </div>
                              ) : (
                                ele.placeholder
                              )}
                            </div>
                          ))}
                      </div>
                    </form>
                  </div>
                </div>
              </LayoutWrapper>
            </div>
          </div>
          <div>
            <Modal
              isOpen={modalIsOpen}
              className="fixed inset-0 flex items-center justify-center p-4"
            >
              <div className="bg-white w-full max-w-xl p-6 rounded-lg shadow-lg overflow-y-auto max-h-[90vh]">
                <div className="flex flex-col items-center flex-wrap gap-4">
                  {getPopupDetails(formQuery)?.map((item: any, i: number) => {
                    return (
                      <div
                        className={`w-full max-h-96 ${
                          item?.type !== "programCards" ? "" : ""
                        }`}
                        key={i}
                      >
                        <DynamicFields
                          arrayIndex={i}
                          getLookupData={
                            dropdownOptions?.[item?.fieldName] || []
                          }
                          watch={() => {}}
                          fromApplicationFilter={true}
                          register={register}
                          fieldItem={item}
                          fieldType={item?.type}
                          fieldName={item?.fieldName}
                          isMandatory={item?.required}
                          label={item?.displayName}
                          isVisibleWhen={checkVisibility(item?.visibleWhen)}
                          selectedValue={
                            applicationFields?.[item?.fieldName] ||
                            getValues()[item?.fieldName] ||
                            null
                          }
                          handleValueChanged={async (
                            value: any,
                            type?: string
                          ) => {
                            const { value: val, label, ...rest } = value;
                            setErrors({});
                            setApplicationFields((prev: any) => {
                              let tempObj = {
                                [item?.fieldName]: value,
                                [item?.fieldDisplayName]: value.label,
                                item: getPopupDetails(formQuery)?.[i + 1],
                              };
                              if (item?.resetChild)
                                tempObj[item?.resetChild] = null;
                              return { ...prev, ...tempObj };
                            });
                            let currentNode = item;
                            while (currentNode?.childField) {
                              getPopupDetails(formQuery)?.forEach(
                                (element: any, index: number) => {
                                  if (
                                    element?.fieldName ==
                                    currentNode?.childField
                                  ) {
                                    setApplicationFields((prev: any) => {
                                      let tempObj: any = {};
                                      if (element?.fieldName)
                                        tempObj[element?.fieldName] = null;
                                      return { ...prev, ...tempObj };
                                    });
                                    currentNode = element;
                                    return;
                                  }
                                }
                              );
                            }
                            setDropdownOptions((prev: any) => {
                              return {
                                ...prev,
                                [item?.childField]: null,
                              };
                            });
                          }}
                          errorMessage={errors[item?.fieldName]}
                          trigger={() => setErrors({})}
                          isLoading={isLoading}
                          required
                        />
                      </div>
                    );
                  })}
                </div>
                <div className="form-action w-full flex justify-center gap-4 mt-6">
                  {getPopupDetails(formQuery)
                    ?.filter((item: any) => item.type == "button")
                    .map((ele: any, i: number) => (
                      <button
                        key={i}
                        className="button button_primary bg-secondary hover:bg-primary w-full lg:w-[260px] mx-auto flex justify-center items-center text-white font-bold h-[41px] rounded cursor-pointer"
                        onClick={() => {
                          let temp = flattenPayload(applicationFields);
                          const isValid = validateOutput(
                            temp,
                            formQuery?.popupDetails?.fieldData
                          );
                          if (isValid.length) {
                            return;
                          }
                          handleSaveApplication();
                        }}
                      >
                        {saving ? (
                          <div className="w-full flex items-center justify-center">
                            <Image
                              priority
                              src={loader2}
                              height={20}
                              width={20}
                              alt="Loading"
                            />
                          </div>
                        ) : (
                          ele.placeholder // submit
                        )}
                      </button>
                    ))}
                  <button
                    onClick={closeModal}
                    className="button button_primary bg-secondary hover:bg-primary w-full lg:w-[260px] mx-auto flex justify-center items-center text-white font-bold h-[41px] rounded cursor-pointer"
                  >
                    Close
                  </button>
                </div>
              </div>
            </Modal>
          </div>
          {showQuestionarrieModal ? (
            <QuestionnairePopup
              isOpen={showQuestionarrieModal}
              onClose={() => setShowQuestionarrieModal(false)}
              fieldData={questionariesSectionQuery.fieldData}
              onSubmit={(data: any) => handleSaveApplication(data)}
              onCancelationApplication={(data: any) =>
                handleCancelationApplication(data)
              }
              applicationFieldss={applicationFields}
              sectionQuery={sectionQuery}
            />
          ) : null}
        </div>
      )}
    </>
  );
};

export default ApplicationFilter;
