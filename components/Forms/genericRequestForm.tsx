"use client";
import React, { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { useQuery } from "@tanstack/react-query";
import {
  getOapFormSections,
  getLookUpData,
  getOapForm,
  getOpportunityDetails,
  apiCall,
  getOapDetail,
  submitChangeRequest,
} from "@/api/api";
import { useAtom } from "jotai";
import {
  consumerAPIKey,
  programmeName,
  email as userEmail<PERSON>tom,
  applicationId as applicationIdAtom,
  brandLogo,
} from "@/lib/atom";
import { preserveQueryParams } from "@/lib/utils";
import DeferralSuccessModal from "../custom/deferral-success-modal";
import { Button } from "../ui/button";
import Image from "next/image";
import loader from "../../public/loader.svg";
import { ScrollArea } from "../ui/scroll-area";
import { toast } from "react-hot-toast";
import { use<PERSON><PERSON>, FormProvider, useFormContext } from "react-hook-form";
import DynamicFields from "../custom/DynamicFields";
import Header from "../custom/header";
import { Skeleton } from "../ui/skeleton";
import { RequestType } from "@/lib/requestTypes";
import { AlertCircle } from "lucide-react";

interface FieldData {
  type: string;
  content?: string | string[];
  title?: string;
  boxStyle?: string;
  displayName?: string;
  fieldName?: string;
  indexOrder?: number;
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
  label?: string;
  rules?: {
    required?: string;
  };
  visibleWhen?: {
    fieldName: string;
    value: string;
  };
  picklistSource?: string;
  picklistSourceType?: string;
  pickListValues?: Array<{ label: string; value: string }>;
}

interface FormContent {
  displayName?: string;
  fieldData?: FieldData[];
  description?: {
    intro?: string;
  };
  fields?: FieldData[];
  mappings?: any;
  displayNameLogo?: string;
  successMessages?: any;
}

interface GenericRequestFormProps {
  requestConfig: RequestType;
}

export default function GenericRequestForm({
  requestConfig,
}: GenericRequestFormProps) {
  const router = useRouter();
  const [apiKey] = useAtom(consumerAPIKey);
  const [program, setProgrammeName] = useAtom(programmeName);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [lookupData, setLookupData] = useState({});
  const [logo, setBrandLogo] = useAtom(brandLogo);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [submissionData, setSubmissionData] = useState<{
    changeRequestId: string;
  } | null>(null);

  const [opportuntityData, setOpportuntityData] = useState<any>({});

  const searchParams = useSearchParams();
  const opportunityId = searchParams.get("opportunityId");

  const [isLoading, setIsLoading] = useState<boolean>(false);

  const methods = useForm();
  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors },
    trigger,
    clearErrors,
    setError,
    getValues,
  } = methods;

  const { data: opportunityDetails, isFetching: opportunityDetailsFetching } =
    useQuery({
      queryKey: [
        `${process.env.NEXT_PUBLIC_OAP_NAME}-opportunity-${opportunityId}`,
      ],
      queryFn: async () => {
        if (opportunityId) {
          return await getOpportunityDetails(opportunityId, apiKey);
        }
        return null;
      },
      enabled: !!(opportunityId && apiKey),
    });

  useEffect(() => {
    if (opportunityDetails) {
      setOpportuntityData({
        ...opportunityDetails,
        oapName: process.env.NEXT_PUBLIC_OAP_NAME,
        currentIntake: opportunityDetails?.currentIntake?.value,
        programId: opportunityDetails?.Programme__c,
        currentProgram: opportunityDetails?.currentProgram?.label,
        programName: opportunityDetails?.ProgrammeName__c,
        applicationId: opportunityDetails?.ApplicationFormId__c,
      });
    }
  }, [opportunityDetails]);

  const { data: pageQuery, isFetching: pageQueryFetching } = useQuery({
    queryKey: [
      `${process.env.NEXT_PUBLIC_OAP_NAME}-${process.env.NEXT_PUBLIC_OAP_MODE}`,
    ],
    queryFn: async () => {
      let res = await getOapDetail(
        {
          name: process.env.NEXT_PUBLIC_OAP_NAME || "",
          mode: process.env.NEXT_PUBLIC_OAP_MODE || "",
        },
        apiKey
      );
      return res;
    },
    enabled: !!apiKey,
  });

  // Fetching the page details
  const { data: formQuery, isFetching: formQueryFetching } = useQuery({
    queryKey: [
      `${process.env.NEXT_PUBLIC_OAP_NAME}-${process.env.NEXT_PUBLIC_OAP_MODE}-${requestConfig.formName}`,
    ],
    queryFn: async () => {
      let res = await getOapForm(
        {
          oap: process.env.NEXT_PUBLIC_OAP_NAME || "",
          mode: process.env.NEXT_PUBLIC_OAP_MODE || "",
          form: requestConfig.formName,
        },
        apiKey
      );
      return res;
    },
    enabled: !!apiKey && !!pageQuery,
  });

  // Fetching form content from the database - Dynamic based on request type
  const { data: formContent, isFetching: formContentFetching } =
    useQuery<FormContent>({
      queryKey: [`${requestConfig.formName}-form-content`],
      queryFn: async () => {
        const res = await getOapFormSections(
          {
            oap: process.env.NEXT_PUBLIC_OAP_NAME || "",
            mode: process.env.NEXT_PUBLIC_OAP_MODE || "",
            formName: requestConfig.formName,
            sectionName: formQuery?.section[0].section,
          },
          apiKey
        );
        return res;
      },
      enabled: !!apiKey && !!formQuery,
    });

  useEffect(() => {
    (async () => {
      if (
        !opportuntityData.programId ||
        !opportuntityData.currentIntake ||
        !opportunityId
      )
        return;
      const fields = formContent?.fieldData || formContent?.fields || [];
      for (const fieldItem of fields) {
        setIsLoading(true);
        if (
          fieldItem.picklistSourceType === "API" &&
          fieldItem.picklistSource
        ) {
          setIsLoading(true);
          try {
            if (fieldItem.picklistSource.includes("${")) {
              const regex = /\${(.*?)}/g;
              const originalString = fieldItem.picklistSource;
              const replacedString = originalString.replace(
                regex,
                (match: string, group: string) => {
                  const value = opportuntityData?.[group];
                  if (value && typeof value === "number") return value;
                  return value?.value || value || match;
                }
              );

              if (!replacedString.includes("${")) {
                const data = await apiCall(replacedString, {
                  method: "GET",
                  key: apiKey,
                });

                const transformedData = Array.isArray(data)
                  ? data.map((item) => ({
                      ...item,
                      label: item.name || item.label || item,
                      value: item.id || item.value || item,
                      displayText: item.name || item.label || item,
                    }))
                  : [];
                setLookupData((prev) => {
                  const updated = {
                    ...prev,
                    [fieldItem.fieldName as string]: transformedData,
                  };
                  return updated;
                });
              }
            } else {
              const data = await getLookUpData(
                fieldItem.picklistSource,
                apiKey
              );
              const dataArray = Array.isArray(data) ? data : [];
              setLookupData((prev) => ({
                ...prev,
                [fieldItem.fieldName as string]: dataArray,
              }));
            }
          } catch (error) {
            console.error(
              `Error fetching lookup data for ${fieldItem.fieldName}:`,
              error
            );
          }
        } else if (
          fieldItem.picklistSourceType === "Inline" &&
          fieldItem.pickListValues
        ) {
          setLookupData((prev) => ({
            ...prev,
            [fieldItem.fieldName as string]: fieldItem.pickListValues,
          }));
        }
      }
      setIsLoading(false);
    })();
  }, [opportunityDetails, apiKey, formContent, opportuntityData]);

  const prefillFormFields = () => {
    if (!formContent || !formContent.mappings || !opportunityDetails) return;
    Object.entries(formContent.mappings).forEach(([fieldName, sourceField]) => {
      if (typeof sourceField === "string") {
        const parts = sourceField.split(".");
        let value = opportunityDetails;
        for (const part of parts) {
          if (value && typeof value === "object" && part in value) {
            value = value[part];
          } else {
            value = undefined;
            break;
          }
        }
        if (value !== undefined) {
          setValue(fieldName, value);
        }
      }
    });
  };

  useEffect(() => {
    if (formContent && opportunityDetails) {
      prefillFormFields();
    }
  }, [formContent, opportunityDetails]);

  const handleCloseSuccessModal = () => {
    setShowSuccessModal(false);
    setSubmissionData(null);
  };

  const handleReturnToDeferral = () => {
    setShowSuccessModal(false);
    setSubmissionData(null);
    const targetPath = preserveQueryParams(
      "/change-request-form",
      searchParams
    );
    router.push(targetPath);
  };

  interface SubmitChangeRequestResponse {
    success: boolean;
    changeRequestId?: string;
    message?: string;
    [key: string]: any;
  }

  const onSubmit = async () => {
    setIsSubmitting(true);

    const values = getValues();

    try {
      // Handle deferral request submission

      const payload = {
        ...values,
        formType: requestConfig.formName,
        opportunityId: opportunityId,
      };

      const queryParams = {
        oap: process.env.NEXT_PUBLIC_OAP_NAME,
        mode: process.env.NEXT_PUBLIC_OAP_MODE,
      };

      const response: SubmitChangeRequestResponse = await submitChangeRequest(
        payload,
        queryParams,
        apiKey
      );

      if (response?.success) {
        // Store submission data and show success modal
        setSubmissionData({
          changeRequestId: response?.changeRequestId || "N/A",
        });
        setShowSuccessModal(true);
      } else {
        throw new Error(
          response?.message || "Failed to submit deferral request"
        );
      }
    } catch (error) {
      console.error("Error submitting form:", error);
      toast.error(`Failed to submit ${requestConfig.name.toLowerCase()}`);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Function to sort fields by indexOrder
  const sortOrder = (fields: any[], orderKey: string) => {
    if (!fields) return [];
    return [...fields].sort((a, b) => {
      if (a[orderKey] === undefined) return 1;
      if (b[orderKey] === undefined) return -1;
      return a[orderKey] - b[orderKey];
    });
  };

  const formFields = formContent?.fieldData || formContent?.fields || [];

  if (!opportunityDetailsFetching && opportunityDetails?.message) {
    return (
      <div className="flex w-full bg-background min-h-screen items-center justify-center">
        <div className="w-full max-w-lg p-6 bg-background border border-gray-200 shadow-lg sm:p-8 flex flex-col items-center gap-4 rounded-lg">
          <div className="w-16 h-16 rounded-full bg-error/10 flex items-center justify-center">
            <AlertCircle className="h-8 w-8 text-error" />
          </div>
          <div className="text-center">
            <h1 className="text-xl font-semibold text-gray-800 mb-2">
              Valid Opportunity ID Required
            </h1>
            <p className="text-gray-600">
              We trouble reading the opportunity details from the provided
              Opportunity Id. Please ensure the provided Opportunity Id is
              valid.
            </p>
          </div>
          <Button
            type="button"
            className="w-full mr-2 bg-white text-black border border-gray-300 hover:bg-gray-100"
            onClick={() => {
              window.location.reload(); // Reload the page
            }}
          >
            <svg
              className="w-5 h-5 mr-2"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
              />
            </svg>
            Refresh Page
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col lg:flex-row min-h-screen w-full">
      <div className="w-full lg:w-64 bg-on-background flex-shrink-0 lg:h-screen">
        <div className="p-4 lg:p-6">
          {pageQueryFetching ? (
            <div className="flex items-center justify-center h-[40px] lg:h-[110px] w-[150px]">
              <Image
                priority
                src={loader}
                height={28}
                width={32}
                alt="Loading..."
              />
            </div>
          ) : (
            <Image
              src={pageQuery?.logoInfo?.signedUrl}
              alt="University Logo"
              width={150}
              height={110}
              priority={true}
              className="object-contain"
            />
          )}
        </div>
      </div>

      <div className="flex flex-col w-full h-[calc(100vh-80px)] lg:h-screen overflow-hidden">
        {/* Header - only visible on desktop */}
        <div className="hidden lg:block sticky top-0 z-50 pt-0">
          <Header
            sectionQueryIsFetching={pageQueryFetching || formContentFetching}
            logo={formQuery?.displayNameLogo}
            logoInfo={pageQuery?.logoInfo}
            title={opportunityDetails?.currentProgram?.label}
          />
        </div>

        <ScrollArea className="flex-1 h-full overflow-auto">
          <div className="pt-[35px] pb-[120px] px-4 md:px-8 lg:pl-20 lg:pr-4">
            {formContent?.displayName && (
              <div className="mt-4">
                <h1 className="text-2xl font-bold text-primary">
                  {formContent?.displayName}
                </h1>
              </div>
            )}
            <div className="max-w-xl">
              {formContentFetching ||
              formQueryFetching ||
              pageQueryFetching ||
              isLoading ||
              opportunityDetailsFetching ? (
                <div className="space-y-6 mt-4">
                  <Skeleton className="h-10 w-3/4 rounded-md" />
                  <Skeleton className="h-4 w-full rounded-md" />
                  <Skeleton className="h-24 w-full rounded-md" />

                  {/* Form field skeletons */}
                  {[1, 2, 3, 4, 5].map((i) => (
                    <div key={i} className="space-y-2">
                      <Skeleton className="h-5 w-1/3 rounded-md" />
                      <Skeleton className="h-12 w-full rounded-md" />
                    </div>
                  ))}

                  {/* Buttons skeleton */}
                  <div className="flex justify-between mt-8 w-full gap-4">
                    <Skeleton className="h-10 w-full rounded-md" />
                    <Skeleton className="h-10 w-full rounded-md" />
                  </div>
                </div>
              ) : (
                <>
                  <FormProvider {...methods}>
                    <form onSubmit={handleSubmit(onSubmit)}>
                      {sortOrder(formFields, "indexOrder").map(
                        (fieldItem, index) => {
                          // Check if this field should be conditionally visible
                          const checkVisibility = (visibleWhenProps: any) => {
                            if (!visibleWhenProps) {
                              return true;
                            }

                            // Handle new "or" logic with multiple conditions
                            if (visibleWhenProps.logic === "or" && Array.isArray(visibleWhenProps.conditions)) {
                              return visibleWhenProps.conditions.some((condition: any) => {
                                const fieldWatchValue = watch(condition.fieldName);

                                if (typeof fieldWatchValue === "object" && fieldWatchValue !== null && !Array.isArray(fieldWatchValue)) {
                                  return fieldWatchValue?.value === condition.value || fieldWatchValue?.label === condition.value;
                                }

                                if (Array.isArray(fieldWatchValue)) {
                                  return fieldWatchValue.some(
                                    (v: any) => v?.value === condition.value || v?.label === condition.value || v === condition.value
                                  );
                                }

                                return fieldWatchValue === condition.value;
                              });
                            }

                            // Handle existing single condition logic
                            const fieldWatchValue = watch(visibleWhenProps.fieldName);
                            return fieldWatchValue === visibleWhenProps.value ||
                              fieldWatchValue?.value === visibleWhenProps.value ||
                              fieldWatchValue?.label === visibleWhenProps.value;
                          };

                          const isVisible = checkVisibility(fieldItem.visibleWhen);

                          // Skip rendering if not visible
                          if (!isVisible) return null;

                          const fieldProps = { ...fieldItem };

                          return (
                            <div key={index} className="mb-6">
                              <DynamicFields
                                register={register}
                                selectedValue={watch(fieldProps.fieldName)}
                                fieldItem={fieldProps}
                                label={
                                  fieldProps.displayName ||
                                  fieldProps.label ||
                                  fieldProps.placeholder
                                }
                                name={fieldProps.fieldName}
                                handleValueChanged={(
                                  value: any,
                                  type?: string
                                ) => {
                                  clearErrors(fieldItem?.fieldName);
                                  if (type === "file") {
                                    setValue(fieldItem.fieldName, value);
                                    return;
                                  }

                                  if (
                                    fieldItem.type === "pickList" &&
                                    fieldItem?.fieldDisplayName
                                  ) {
                                    setValue(
                                      fieldItem?.fieldDisplayName,
                                      typeof value === "object"
                                        ? value.label
                                        : value
                                    );
                                  }

                                  if (fieldItem?.subField) {
                                    const { subFieldName, subFieldValue } =
                                      fieldItem?.subField;
                                    setValue(
                                      subFieldName,
                                      value[subFieldValue]
                                    );
                                  }

                                  if (fieldItem?.resetChild) {
                                    if (Array.isArray(fieldItem.resetChild)) {
                                      fieldItem?.resetChild.forEach(
                                        (child: any) => {
                                          setValue(child, "");
                                          setValue(`${child}DisplayName`, "");
                                          clearErrors(child);
                                        }
                                      );
                                    } else {
                                      setValue(fieldItem?.resetChild, "");
                                      setValue(
                                        `${fieldItem?.resetChild}DisplayName`,
                                        ""
                                      );
                                      clearErrors(fieldItem?.resetChild);
                                    }
                                  }
                                  setValue(
                                    fieldItem?.fieldName,
                                    typeof value === "object"
                                      ? value.value
                                      : value
                                  );
                                }}
                                errorMessage={
                                  errors[fieldProps.fieldName]?.message
                                }
                                trigger={trigger}
                                clearErrors={clearErrors}
                                setError={setError}
                                watch={watch}
                                setValue={setValue}
                                isVisibleWhen={true}
                                getLookupData={
                                  Array.isArray(
                                    lookupData[
                                      fieldProps.fieldName as keyof typeof lookupData
                                    ]
                                  )
                                    ? lookupData[
                                        fieldProps.fieldName as keyof typeof lookupData
                                      ]
                                    : []
                                }
                                disabled={fieldProps.disabled}
                                fromApplicationFilter={true}
                                enableValidation={true}
                              />
                            </div>
                          );
                        }
                      )}

                      <div className="flex justify-between mt-8 w-full">
                        <Button
                          type="button"
                          className="w-full mr-2 bg-white text-black border border-gray-300 hover:bg-gray-100"
                          onClick={() => {
                            const targetPath = preserveQueryParams(
                              "/change-request-form",
                              searchParams
                            );
                            router.push(targetPath);
                          }}
                        >
                          Cancel
                        </Button>

                        <Button
                          type="submit"
                          className="w-full ml-2 bg-on-background hover:bg-on-background text-white"
                          disabled={isSubmitting}
                        >
                          {isSubmitting ? (
                            <div className="flex items-center justify-center">
                              <Image
                                src={loader}
                                alt="Loading"
                                width={20}
                                height={20}
                                className="mr-2"
                              />
                              Submitting...
                            </div>
                          ) : (
                            "Submit"
                          )}
                        </Button>
                      </div>
                    </form>
                  </FormProvider>
                </>
              )}
            </div>
          </div>
        </ScrollArea>
      </div>

      {/* Deferral Success Modal */}
      {submissionData && (
        <DeferralSuccessModal
          isOpen={showSuccessModal}
          onClose={handleCloseSuccessModal}
          onReturnToDeferral={handleReturnToDeferral}
          successMessages={formContent?.successMessages}
        />
      )}
    </div>
  );
}
