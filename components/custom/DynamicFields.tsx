import { useEffect, useState } from "react";
import { useForm<PERSON>ontext, useWatch } from "react-hook-form";
import Image from "next/image";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Trash2, AlertTriangle } from "lucide-react";
import ReactMarkdown from "react-markdown";
import { useQuery } from "@tanstack/react-query";
import { useAtom } from "jotai";
import { Input } from "../ui/input";
import DatePicker from "./date-picker";
import FilePicker from "./file-picker";
import { FieldTitle } from "./FieldTitle";
import { RadioButton } from "./radioButton";
import { MobileInput } from "./mobile-input";
import { Label } from "../ui/label";
import DropDownBox from "../ui/dropdown";
import {
  Table,
  TableBody,
  TableCell,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Textarea } from "@/components/ui/textarea";
import { CheckBox } from "./checkBox";
import { useLookUpData } from "@/hooks/useLookupData";
import { getDocumentName } from "@/api/api";
import { sortOrder } from "@/helpers/Sorting";
import check from "../../public/check.svg";
import uncheck from "../../public/uncheck.svg";
import { Separator } from "../ui/separator";
import {
  applicationId,
  consumerAPIKey,
  nextForm,
  preferredLanguage,
  qualifyingQuestions,
  staticContentsAtom,
  email as userEmailId,
} from "@/lib/atom";
import MonthPicker from "./MonthPicker";
import { LinkRenderer } from "./linkRender";
import { formatDate, replacePlaceholders } from "@/lib/utils";
import { preferredDateFormat } from "@/lib/atom";
import { isValidPhoneNumber } from "libphonenumber-js";
import { getPhoneNumberWithoutLastDigit } from "@/helpers/FormatPhoneNumber";
import Signature from "./signature";
import remarkGfm from "remark-gfm";
import rehypeRaw from "rehype-raw";
import YearPicker from "./year-picker";
import { fontSizeAtom } from "@/lib/atom";
import { getBrandSpecificFontStyle } from "@/lib/brandUtils";
import TextWithHyperlink from "./TextWithHyperlink";

// Helper function to render text with inline links
const renderInlineWithLinks = (text: string) => {
  if (!text) return text;

  // Match markdown links [text](url)
  const linkRegex = /\[([^\]]+)\]\(([^)]+)\)/g;
  const parts = [];
  let lastIndex = 0;
  let match;

  while ((match = linkRegex.exec(text)) !== null) {
    // Add text before the link
    if (match.index > lastIndex) {
      parts.push(text.substring(lastIndex, match.index));
    }

    // Add the link
    parts.push(
      <a
        key={match.index}
        href={match[2]}
        target="_blank"
        rel="noreferrer"
        className="text-primary underline"
      >
        {match[1]}
      </a>,
    );

    lastIndex = match.index + match[0].length;
  }

  // Add remaining text
  if (lastIndex < text.length) {
    parts.push(text.substring(lastIndex));
  }

  return parts.length > 1 ? parts : text;
};

interface DynamicFieldsProps {
  errorMessage?: string;
  fieldItem: any;
  handleValueChanged: (value: any, type?: string) => void;
  getLookupData: any;
  isFileSuccess?: boolean;
  label: string;
  limit?: number;
  selectedValue?: any;
  setIsFileSuccess?: (value: boolean) => void;
  uploadDocs?: any;
  name?: string;
  register?: any;
  arrayIndex: number;
  handleDeleteFile?: Function;
  trigger?: any;
  isLoading?: boolean | undefined;
  rest: any | undefined;
  getStudent?: any | undefined;
  studentData?: any;
  isVisibleWhen?: boolean;
  watch?: any;
  clearErrors?: any;
  fromApplicationFilter?: boolean;
  enableValidation?: boolean;
  setValue?: any;
  setError?: any;
  subSectionIndex?: any;
  handleRemove?: any;
  fieldName?: any;
  displayNoTitle?: boolean;
  disabled?: boolean;
  isFilteredField?: boolean;
  selectedAnswers?: any;
  maxOcrReprocessCount: number;
  isOcrReprocess?: boolean;
  enableAutoDetection?: boolean;
  subIndex?: number;
}

export default function DynamicFields(props: any) {
  const {
    handleDeleteFile,
    arrayIndex,
    name,
    getLookupData,
    fieldItem,
    label,
    selectedValue,
    uploadDocs,
    handleValueChanged,
    register,
    errorMessage,
    trigger,
    isLoading,
    isVisibleWhen,
    watch,
    fromApplicationFilter,
    enableValidation = false,
    clearErrors,
    setValue,
    studentData,
    subSectionIndex,
    setError,
    fieldName,
    handleRemove,
    displayNoTitle = true,
    disabled,
    isFilteredField,
    selectedAnswers,
    isOcrReprocess,
    enableAutoDetection,
    maxOcrReprocessCount = 2,
    subIndex,
    ...rest
  }: DynamicFieldsProps = props;

  const {
    type: fieldType,
    table,
    placeholder,
    rules,
    sectionCanRepeat,
    options,
    required: isMandatory,
    filter: filterQualify,
    filterRules,
    text,
  } = fieldItem;
  const patternObject = rules?.["pattern"];

  if (patternObject && patternObject?.["value"]) {
    const regexValue = new RegExp(patternObject?.["value"]);

    patternObject["value"] = regexValue;
  }
  const {
    control,
    formState: { errors },
    getValues,
  }: any = useFormContext();

  const watchedFields = useWatch({
    control,
  });

  const [lookupOptions, setLookupOptions] = useState<any>([]);
  const [nextFormDetails]: any = useAtom(nextForm);
  const [applicantId] = useAtom(applicationId);
  const [emailId] = useAtom(userEmailId);
  const [apiKey] = useAtom(consumerAPIKey);
  const [preferredDate] = useAtom(preferredDateFormat);
  const [isDropDownLoading, setIsDropDownLoading] = useState(false);
  const { data: fetchLookUp } = useLookUpData();
  const [qualifyingQuestionsState, _] = useAtom(qualifyingQuestions);
  const [preferLang] = useAtom(preferredLanguage);
  const [staticContent] = useAtom<any>(staticContentsAtom);
  const [fontSize] = useAtom(fontSizeAtom);

  const useGetLookupData = async (fieldValue: any) => {
    const values = fieldValue?.picklistSourceType;
    if (!fromApplicationFilter) {
      if (values !== "API") {
        return fieldValue?.pickListValues;
      } else if (fieldValue?.picklistSource?.includes("${")) {
        const regex = /\${(.*?)}/g; // Using 'g' flag for global matching
        const originalString = fieldValue?.picklistSource;

        let value: any;
        if (name?.includes(".")) {
          const [section, index] = name?.split(".");
          value = watchedFields[section]?.[parseInt(index)];
        } else {
          value = watchedFields;
        }

        const replacedString = originalString.replace(
          regex,
          (match: any, group: any) => {
            const matchedValue = value[group];

            // Checking if the value is a number and converting
            if (typeof matchedValue == "object" && !isNaN(matchedValue?.value))
              return Number(matchedValue.value);
            if (!isNaN(matchedValue)) return Number(matchedValue);
            return matchedValue?.value || matchedValue || match; // Use matched value or keep the original match if undefined
          },
        );

        if (
          !replacedString.includes("undefined") &&
          !replacedString.includes("${")
        ) {
          const res = await fetchLookUp({
            queryKey: fieldValue?.fieldName,
            name: replacedString,
            preferredLanguage: preferLang,
          });
          return res;
        }
      } else {
        const res = await fetchLookUp({
          queryKey: fieldValue?.fieldName,
          name: fieldValue?.picklistSource,
          preferredLanguage: preferLang,
        });
        return res;
      }
    }
  };

  // Rename from useGetRadioData to getRadioData (remove 'use' prefix)
  const getRadioData = async (fieldValue: any) => {
    if (!fromApplicationFilter) {
      if (fieldValue?.radiobuttonSourceType !== "API") {
        return fieldValue?.radiobuttonValues;
      } else if (fieldValue?.radiobuttonSource?.includes("${")) {
        const regex = /\${(.*?)}/g;
        const originalString = fieldValue?.radiobuttonSource;

        let value: any;
        if (name?.includes(".")) {
          const [section, index] = name?.split(".");
          value = watchedFields[section]?.[parseInt(index)];
        } else {
          value = watchedFields;
        }

        const replacedString = originalString.replace(
          regex,
          (match: any, group: any) => {
            const matchedValue = value[group];
            // If matchedValue is not present, return an empty string to remove this parameter
            if (!matchedValue) {
              return "";
            }
            if (typeof matchedValue == "object" && !isNaN(matchedValue?.value))
              return Number(matchedValue.value);
            if (!isNaN(matchedValue)) return Number(matchedValue);
            return matchedValue?.value || matchedValue || "";
          },
        );

        if (
          !replacedString.includes("undefined") &&
          !replacedString.includes("${")
        ) {
          const res = await fetchLookUp({
            queryKey: fieldValue?.fieldName,
            name: replacedString,
            ...(preferLang === "de" && { language: "de" }),
          });
          return res;
        }
      } else {
        const res = await fetchLookUp({
          queryKey: fieldValue?.fieldName,
          name: fieldValue?.radiobuttonSource,
        });
        return res;
      }
    }
  };

  // Add state for radio options
  const [radioOptions, setRadioOptions] = useState<
    Array<{ value: string; label: string }>
  >([]);
  const [isRadioLoading, setIsRadioLoading] = useState(false);

  const fetchRadioOptions = async () => {
    try {
      setIsRadioLoading(true);
      const options = await getRadioData(fieldItem);
      setRadioOptions(options || []);
    } catch (error) {
      console.error("Error fetching radio options:", error);
    } finally {
      setIsRadioLoading(false);
    }
  };

  useEffect(() => {
    if (
      fieldType === "radioButton" &&
      fieldItem.radiobuttonSourceType === "API"
    ) {
      fetchRadioOptions();
    }
  }, [fieldItem, fieldType]);

  useEffect(() => {
    if (errors) {
      const errorFieldNames = Object.keys(errors);
      const errorValues = Object.values(errors);
      const ErrorName = errorValues[0];
      if (Array.isArray(ErrorName)) {
        const firstErrorFieldName = errorFieldNames[0];

        ErrorName?.map((item, index) => {
          const subSectionFieldNames = Object.keys(item);
          if (subSectionFieldNames.length > 0) {
            const firstFieldName = subSectionFieldNames[0];
            const selectedElement = document.getElementById(
              `${firstErrorFieldName}.${index}.${firstFieldName}`,
            );

            if (selectedElement) {
              selectedElement.scrollIntoView({
                behavior: "smooth",
                block: "nearest",
              });
            }
          }
        });
      } else {
        if (errorFieldNames.length > 0) {
          const firstErrorFieldName = errorFieldNames[0];
          const selectElement = document.getElementById(firstErrorFieldName);

          if (selectElement) {
            selectElement.scrollIntoView({
              behavior: "smooth",
              block: "nearest",
            });
          }
        }
      }
    }
  }, [errors, errorMessage]);

  function findDocumentsById(ids: string[], documents: any[]) {
    return documents?.filter((doc: any) => ids?.includes(doc?.documentId));
  }

  useEffect(() => {
    const fetchDocument = async () => {
      if (
        fieldItem?.type === "document" ||
        fieldItem?.type === "multiDocument" ||
        fieldItem?.type === "signature"
      ) {
        let userDoc: any = {};
        if (fieldItem?.type === "document") {
          userDoc = {
            oapName: nextFormDetails?.oap,
            email: emailId,
            type: fieldItem?.documentType,
            applicationId: applicantId,
            documentId: watch(name)?.[0]?.documentId || watch(name)?.[0],
          };
        } else {
          userDoc = {
            oapName: nextFormDetails?.oap,
            email: emailId,
            type: fieldItem?.documentType,
            applicationId: applicantId,
            documentId: watch(name),
          };
        }

        if (
          fieldItem.type === "multiDocument" &&
          userDoc?.documentId?.length > 0
        ) {
          let temp = userDoc?.documentId;

          if (Array.isArray(temp)) {
            temp = temp.map((item) => item?.documentId);
            delete userDoc.documentId;
          }
          const resTemp = await getDocumentName(userDoc, apiKey);

          if (Array.isArray(resTemp)) {
            const res = findDocumentsById(temp, resTemp);

            if (fieldItem?.type === "multiDocument" && Array.isArray(res)) {
              handleValueChanged([...res], "file");
            }
          }
        }

        if (fieldItem.type === "document") {
          const res = await getDocumentName(userDoc, apiKey);

          if (
            fieldItem?.type === "document" &&
            res?.Item &&
            Object.keys(res?.Item).length > 0
          ) {
            handleValueChanged([res?.Item], "file");
          }
        }
      }
    };

    fetchDocument();
  }, [fieldItem?.type]);

  const fetchOptionsData = async (value?: any) => {
    try {
      // eslint-disable-next-line react-hooks/rules-of-hooks
      const options = await useGetLookupData(fieldItem);
      setLookupOptions(options);
      setIsDropDownLoading(false);
      return options;
    } catch (error) {
      console.error("Error fetching lookup data:", error);
    }
  };

  useEffect(() => {
    if (fieldItem.type == "pickList" || fieldItem.type == "multiPickList") {
      fetchOptionsData();
    }
  }, [fieldItem]);

  useEffect(() => {
    const res = async () => {
      if (
        fieldItem.prefilledPicklistValue &&
        (fieldItem.type === "pickList" || fieldItem.type === "multiPickList")
      ) {
        if (fieldItem.picklistSourceType === "API") {
          try {
            const options = await fetchOptionsData();
            const defaultValue = options?.[0];

            if (defaultValue) {
              setValue(
                fieldItem.fieldName,
                getValues(fieldItem.fieldName)
                  ? getValues(fieldItem.fieldName)
                  : defaultValue,
              );
            }
          } catch (error) {
            console.error("Error fetching picklist options:", error);
          }
        } else if (fieldItem.picklistSourceType === "INLINE") {
          const inlineOptions = fieldItem.pickListValues;

          if (inlineOptions && inlineOptions.length > 0) {
            const defaultValue = inlineOptions[0];

            setValue(
              fieldItem.fieldName,
              getValues(fieldItem.fieldName)
                ? getValues(fieldItem.fieldName)
                : defaultValue,
            );
          }
        }
      }
      if (
        fieldItem.resetChild &&
        fieldItem.type === "pickList" &&
        fieldItem?.default
      ) {
        const order = await fetchOptionsData();
        const fieldValue = order?.[0]?.[fieldItem?.dependentFieldName];
        const stringValue = String(fieldValue);

        if (Array.isArray(fieldItem?.resetChild)) {
          fieldItem?.resetChild?.forEach((child: string) => {
            setValue(child, stringValue || "");
          });
        } else {
          setValue(fieldItem?.resetChild, stringValue || "");
        }
      }
      if (
        fieldItem?.defaultFieldValue &&
        fieldItem.type === "pickList" &&
        fieldItem?.default
      ) {
        setValue(fieldItem.fieldName, fieldItem?.defaultFieldValue || "");
      }
      if (fieldItem?.defaultWhen) {
        let defaultWhenObj = fieldItem?.defaultWhen;
        let expression = defaultWhenObj?.condition;

        if (
          expression == "greaterThan" &&
          defaultWhenObj?.fieldValue <= watch(defaultWhenObj?.fieldName)
        ) {
          setValue(fieldItem?.fieldName, fieldItem?.defaultWhen?.value);
        }
        if (
          expression == "strictlyGreaterThan" &&
          defaultWhenObj?.fieldValue < watch(defaultWhenObj?.fieldName)
        ) {
          setValue(fieldItem?.fieldName, fieldItem?.defaultWhen?.value);
        }
      }
    };
    res();
  }, [fieldItem]);

  useEffect(() => {
    if (!fieldItem?.defaultFieldValue) return;
    if (fieldItem?.defaultFieldValue && isVisibleWhen) {
      setValue(name, fieldItem?.defaultFieldValue);
    }
  }, [name, fieldItem?.defaultFieldValue, selectedValue, isVisibleWhen]);

  useEffect(() => {
    const handleResetValues = (fieldName: any) => {
      const values = getValues();
      const filteredFields = Object.keys(values).filter((key) => {
        return key === fieldName || key === fieldName + "DisplayName";
      });

      filteredFields.forEach((field) => {
        setValue(field, "");
      });
    };

    const isVisibleWhenNo = () => {
      const isVisibleNo =
        watch(fieldItem?.visibleWhen?.fieldName) === "No" ||
        watch(fieldItem?.visibleWhen?.fieldName)?.label === "No";
      const isDifferentFromFieldName =
        name !== fieldItem?.visibleWhen?.fieldName;
      return (
        isVisibleNo &&
        isDifferentFromFieldName &&
        fieldItem?.visibleWhen?.value !== "No"
      );
    };

    if (isVisibleWhenNo()) {
      if (fieldItem?.subSection) {
        fieldItem?.[fieldItem?.subSection]?.fieldData?.forEach((item: any) => {
          handleResetValues(item?.fieldName);
        });
      } else {
        handleResetValues(name);
      }
    }
  }, [watch(fieldItem?.visibleWhen?.fieldName), name, fieldItem]);

  const checkVisibility = (
    visibleWhenProps: any,
    fieldName?: any,
    subSectionIndex?: any,
  ) => {
    if (!visibleWhenProps) {
      return true;
    }

    // Handle new "or" logic with multiple conditions
    if (visibleWhenProps.logic === "or" && Array.isArray(visibleWhenProps.conditions)) {
      return visibleWhenProps.conditions.some((condition: any) => {
        const fieldWatchValue = fieldName
          ? watch(fieldName)?.[subSectionIndex]?.[condition.fieldName]
          : watch(condition.fieldName);

        if (typeof fieldWatchValue === "object" && fieldWatchValue !== null && !Array.isArray(fieldWatchValue)) {
          return fieldWatchValue?.value === condition.value || fieldWatchValue?.label === condition.value;
        }

        if (Array.isArray(fieldWatchValue)) {
          return fieldWatchValue.some(
            (v: any) => v?.value === condition.value || v?.label === condition.value || v === condition.value
          );
        }

        return fieldWatchValue === condition.value;
      });
    }

    if (Array.isArray(visibleWhenProps)) {
      // Check visibility for each object in the array
      return visibleWhenProps.some((prop: any) => {
        const fieldWatchValue = fieldName
          ? watch(fieldName)?.[subSectionIndex]?.[prop.fieldName]
          : watch(prop.fieldName);

        let isArr = Array.isArray(fieldWatchValue);

        if (Array.isArray(prop.value)) {
          let valueReq = watch(prop?.fieldName);

          const valueArr = Array.isArray(prop.value)
            ? prop.value
            : [prop.value];

          if (
            valueArr.some((value: any) => value === fieldWatchValue?.label) ||
            valueArr.some((value: any) => value === fieldWatchValue) ||
            valueArr.some((value: any) => value === fieldWatchValue?.value) ||
            valueArr.some((value: any) => value === valueReq)
          ) {
            return true;
          }
        }

        if (typeof fieldWatchValue === "object" && !isArr) {
          if (fieldWatchValue?.value === prop?.value) {
            return true;
          }
          return false;
        }

        if (prop?.value) {
          return (
            fieldWatchValue === prop?.value ||
            (isArr &&
              fieldWatchValue?.some((obj: any) => obj.value === prop?.value))
          );
        }

        return true;
      });
    } else {
      const fieldWatchValue = fieldName
        ? watch(fieldName)?.[subSectionIndex]?.[visibleWhenProps.fieldName]
        : watch(visibleWhenProps.fieldName);
      const { condition, value } = visibleWhenProps;

      let isArr = Array.isArray(fieldWatchValue);

      if (Array.isArray(visibleWhenProps.value)) {
        let valueReq = watch(visibleWhenProps?.fieldName);
        // Check if visibleWhenProps.value is an array
        const valueArr = Array.isArray(visibleWhenProps.value)
          ? visibleWhenProps.value
          : [visibleWhenProps.value];

        // Check if any value in the valueArr matches fieldWatchValue
        if (
          valueArr.some((value: any) => value === fieldWatchValue?.label) ||
          valueArr.some((value: any) => value === fieldWatchValue) ||
          valueArr.some((value: any) => value === fieldWatchValue?.value) ||
          valueArr.some((value: any) => value === valueReq)
        ) {
          return condition === "notEqual" ? false : true;
        }
      }
      if (typeof fieldWatchValue === "object" && !isArr) {
        if (condition === "notEqual") {
          return !value.some((v: any) => v.value === fieldWatchValue?.value);
        }
        if (condition === "equal") {
          return value.some((v: any) => v.value === fieldWatchValue?.value);
        }
        if (fieldWatchValue?.value === visibleWhenProps?.value) {
          return true;
        } else return false;
      }
      if (visibleWhenProps?.value) {
        if (condition === "notEqual") {
          return !(
            visibleWhenProps?.value.some(
              (item: { value: string }) =>
                item.value === fieldWatchValue || item === fieldWatchValue,
            ) ||
            (isArr &&
              fieldWatchValue?.some((obj: any) =>
                value.some((v: any) => v.value === obj.value),
              )) ||
            (Array.isArray(visibleWhenProps?.value) &&
              visibleWhenProps?.value.includes(fieldWatchValue))
          );
        }
        if (condition === "equal") {
          return (
            visibleWhenProps?.value.some(
              (item: { value: string }) =>
                item.value === fieldWatchValue || item === fieldWatchValue,
            ) ||
            (isArr &&
              fieldWatchValue.some((obj: any) =>
                value.some(
                  (item: { value: string }) => item.value === obj.value,
                ),
              ))
          );
        }
        return fieldWatchValue === visibleWhenProps?.value ||
          (isArr &&
            fieldWatchValue?.some(
              (obj: any) => obj.value === visibleWhenProps?.value,
            ))
          ? true
          : false;
      }
      return true;
    }
  };

  // Preview-specific visibility check function that uses studentData instead of watch
  const checkVisibilityForPreview = (
    visibleWhenProps: any,
    studentData: any,
    fieldName?: any,
    subSectionIndex?: any,
  ) => {
    if (!visibleWhenProps) {
      return true;
    }

    // Handle new "or" logic with multiple conditions
    if (
      visibleWhenProps.logic === "or" &&
      Array.isArray(visibleWhenProps.conditions)
    ) {
      return visibleWhenProps.conditions.some((condition: any) => {
        const fieldValue = fieldName
          ? studentData?.[fieldName]?.[subSectionIndex]?.[condition.fieldName]
          : studentData?.[condition.fieldName];

        if (
          typeof fieldValue === "object" &&
          fieldValue !== null &&
          !Array.isArray(fieldValue)
        ) {
          return (
            fieldValue?.value === condition.value ||
            fieldValue?.label === condition.value
          );
        }

        if (Array.isArray(fieldValue)) {
          return fieldValue.some(
            (v: any) =>
              v?.value === condition.value ||
              v?.label === condition.value ||
              v === condition.value,
          );
        }

        return fieldValue === condition.value;
      });
    }

    if (Array.isArray(visibleWhenProps)) {
      // Check visibility for each object in the array
      return visibleWhenProps.some((prop: any) => {
        const fieldValue = fieldName
          ? studentData?.[fieldName]?.[subSectionIndex]?.[prop.fieldName]
          : studentData?.[prop.fieldName];

        let isArr = Array.isArray(fieldValue);

        if (Array.isArray(prop.value)) {
          let valueReq = studentData?.[prop?.fieldName];

          const valueArr = Array.isArray(prop.value)
            ? prop.value
            : [prop.value];

          if (
            valueArr.some((value: any) => value === fieldValue?.label) ||
            valueArr.some((value: any) => value === fieldValue) ||
            valueArr.some((value: any) => value === fieldValue?.value) ||
            valueArr.some((value: any) => value === valueReq)
          ) {
            return true;
          }
        }

        if (typeof fieldValue === "object" && !isArr) {
          if (fieldValue?.value === prop?.value) {
            return true;
          }
          return false;
        }

        if (prop?.value) {
          return (
            fieldValue === prop?.value ||
            (isArr && fieldValue?.some((obj: any) => obj.value === prop?.value))
          );
        }

        return true;
      });
    } else {
      const fieldValue = fieldName
        ? studentData?.[fieldName]?.[subSectionIndex]?.[
            visibleWhenProps.fieldName
          ]
        : studentData?.[visibleWhenProps.fieldName];
      const { condition, value } = visibleWhenProps;

      let isArr = Array.isArray(fieldValue);

      // Handle multiCheckbox array values first - check if the target value is in the array
      if (isArr && visibleWhenProps?.value) {
        return fieldValue.includes(visibleWhenProps.value);
      }

      if (Array.isArray(visibleWhenProps.value)) {
        let valueReq = studentData?.[visibleWhenProps?.fieldName];
        // Check if visibleWhenProps.value is an array
        const valueArr = Array.isArray(visibleWhenProps.value)
          ? visibleWhenProps.value
          : [visibleWhenProps.value];

        // Check if any value in the valueArr matches fieldValue
        if (
          valueArr.some((value: any) => value === fieldValue?.label) ||
          valueArr.some((value: any) => value === fieldValue) ||
          valueArr.some((value: any) => value === fieldValue?.value) ||
          valueArr.some((value: any) => value === valueReq)
        ) {
          return condition === "notEqual" ? false : true;
        }
      }
      if (typeof fieldValue === "object" && !isArr) {
        if (condition === "notEqual") {
          return !value.some((v: any) => v.value === fieldValue?.value);
        }
        if (condition === "equal") {
          return value.some((v: any) => v.value === fieldValue?.value);
        }
        if (fieldValue?.value === visibleWhenProps?.value) {
          return true;
        } else return false;
      }
      if (visibleWhenProps?.value) {
        if (condition === "notEqual") {
          return !(
            visibleWhenProps?.value.some(
              (item: { value: string }) =>
                item.value === fieldValue || item === fieldValue,
            ) ||
            (isArr &&
              fieldValue?.some((obj: any) =>
                value.some((v: any) => v.value === obj.value),
              )) ||
            (Array.isArray(visibleWhenProps?.value) &&
              visibleWhenProps?.value.includes(fieldValue))
          );
        }
        if (condition === "equal") {
          return (
            visibleWhenProps?.value.some(
              (item: { value: string }) =>
                item.value === fieldValue || item === fieldValue,
            ) ||
            (isArr &&
              fieldValue.some((obj: any) =>
                value.some(
                  (item: { value: string }) => item.value === obj.value,
                ),
              ))
          );
        }
        // Handle array values (multiCheckbox)
        if (isArr) {
          return fieldValue.includes(visibleWhenProps?.value);
        }

        // Handle object values
        if (typeof fieldValue === "object" && fieldValue !== null) {
          return fieldValue?.value === visibleWhenProps?.value;
        }

        // Handle simple values
        return fieldValue === visibleWhenProps?.value;
      }
      return true;
    }
  };

  const showOnly = (field: any) => {
    const fieldData = studentData?.[field?.fieldName];
    if (!checkVisibilityForPreview(field?.visibleWhen, studentData))
      return null;

    if (field?.type === "pickList") {
      return fieldData?.[`${field?.fieldName}DisplayName`] || " ";
    } else if (field?.type === "number") {
      return fieldData?.numberWithCode || " ";
    } else if (field?.type === "multiCheckbox") {
      if (!fieldData) return " ";

      // Handle options format (array of strings)
      if (Array.isArray(fieldData)) {
        return fieldData.join(", ");
      }

      // Handle checkboxes format (object with checkbox values)
      if (typeof fieldData === "object") {
        const selectedOptions = Object.entries(fieldData)
          .filter(([key, value]) => value === true)
          .map(([key, value]) => {
            // Try to find the display name from field configuration
            const checkbox = field?.checkboxes?.find(
              (cb: any) => cb.fieldName === key,
            );
            return checkbox?.displayName || checkbox?.label || key;
          });
        return selectedOptions.join(", ");
      }

      return fieldData.toString();
    } else if (typeof fieldData === "object") {
      return fieldData?.[field?.fieldName] || " ";
    } else {
      return fieldData || " ";
    }
  };

  const formatDateForDisplay = (dateString: string): Date | undefined => {
    if (!dateString || dateString === "") return undefined;

    try {
      // Parse the date string and create a new Date object
      const [year, month, day] = dateString.split("-").map(Number);
      const date = new Date(year, month - 1, day);

      // Check if the date is valid
      if (isNaN(date.getTime())) return undefined;

      return date;
    } catch (error) {
      console.error("Error formatting date:", error);
      return undefined;
    }
  };

  function getFieldDataValue(fieldData: any, studentData: any) {
    if (fieldData?.type === "pickList") {
      return studentData?.[`${fieldData?.fieldName}DisplayName`] || " ";
    } else if (fieldData?.type === "number") {
      return studentData?.[fieldData?.fieldName]?.numberWithCode || " ";
    } else if (fieldData?.type === "multiCheckbox") {
      const fieldValue = studentData?.[fieldData?.fieldName];
      if (!fieldValue) return " ";

      // Handle options format (array of strings)
      if (Array.isArray(fieldValue)) {
        return fieldValue.join(", ");
      }

      // Handle checkboxes format (object with checkbox values)
      if (typeof fieldValue === "object") {
        const selectedOptions = Object.entries(fieldValue)
          .filter(([key, value]) => value === true)
          .map(([key, value]) => {
            // Try to find the display name from field configuration
            const checkbox = fieldData?.checkboxes?.find(
              (cb: any) => cb.fieldName === key,
            );
            return checkbox?.displayName || checkbox?.label || key;
          });
        return selectedOptions.join(", ");
      }

      return fieldValue.toString();
    } else if (typeof studentData[fieldData?.fieldName] === "object") {
      return studentData?.[fieldData?.fieldName]?.[fieldData?.fieldName] || " ";
    } else if (fieldData?.type === "date") {
      return (
        formatDate(
          formatDateForDisplay(studentData[fieldData?.fieldName]),
          preferredDate,
        ) || " "
      );
    } else {
      return studentData[fieldData?.fieldName] || " ";
    }
  }

  // if visibility is not true return out void
  if (fieldItem?.visibility == false) return;

  // Header field type
  if (fieldType === "header" && isVisibleWhen) {
    const headerBackground = fieldItem?.headerBackground || "#e5e7eb"; // Default gray background
    return (
      <header
        className="w-full mb-5 py-2 pl-3 -mr-10 sm:-mr-12 md:-mr-16 pr-10 sm:pr-12 md:pr-16"
        style={{ backgroundColor: headerBackground }}
        id={name}
        role="banner"
        aria-label={fieldItem?.text || "Section header"}
      >
        <h2
          className="font-semibold text-lg text-gray-600 m-0"
          style={getBrandSpecificFontStyle(fontSize, "header")}
        >
          <TextWithHyperlink
            text={fieldItem?.text || ""}
            hyperLinkText={fieldItem?.hyperLinkText}
            hyperLinkValue={fieldItem?.hyperLinkValue}
          />
        </h2>
      </header>
    );
  }

  if (fieldItem?.default == true && fieldType === "pickList" && isVisibleWhen) {
    return (
      <div className="w-full mb-5" id={name}>
        <FieldTitle
          label={label}
          isMandatory={isMandatory}
          helpText={fieldItem?.helpText}
          hyperLinkText={fieldItem?.hyperLinkText}
          hyperLinkValue={fieldItem?.hyperLinkValue}
        />
        <DropDownBox
          selectedValue={[
            { label: studentData?.[fieldItem?.fieldDisplayName] },
          ]}
          placeholder={""}
          items={[]}
          onChange={(selectedValue: any) => {
            handleValueChanged(selectedValue);
          }}
          isDisabled
        />
        <span>{fieldItem?.displaySubInformation}</span>
        {errorMessage && (
          <span className="text-sm text-[red] h-0 mb-2">{errorMessage}</span>
        )}
        <TextWithHyperlink
          text={fieldItem?.text}
          hyperLinkText={fieldItem?.hyperLinkText}
          hyperLinkValue={fieldItem?.hyperLinkValue}
        />
      </div>
    );
  }

  if ((fieldType === "mobile" || fieldType == "number") && isVisibleWhen) {
    let numberRules = {
      ...rules,
      validate: (value: any) => {
        const fullNumber = value?.number;
        const numberCode = value?.dialCode;
        if (numberCode && fullNumber) {
          let res = isValidPhoneNumber(`+${numberCode}${fullNumber}`);

          if (!res && fullNumber)
            return (
              staticContent?.errors?.userValidation?.invalidNumber ||
              "Invalid Number"
            );
          return res;
        }
      },
    };
    return (
      <div className="w-full mb-5" id={name}>
        {displayNoTitle && (
          <FieldTitle
            label={label}
            isMandatory={isMandatory}
            helpText={fieldItem?.helpText}
            hyperLinkText={fieldItem?.hyperLinkText}
            hyperLinkValue={fieldItem?.hyperLinkValue}
          />
        )}

        <MobileInput
          handleChange={(
            status: any,
            phone: any,
            country: any,
            formattedNumber: any,
          ) => {
            const sanitizedPhoneNumber = phone.replace(/\D/g, "");
            const sanitizedFullNumber = formattedNumber.replace(/[^\d+]/g, "");
            const fullNumber = getPhoneNumberWithoutLastDigit(
              formattedNumber.replace(/[^\d+]/g, ""),
            );

            if (phone.length > 12) return;

            if (phone.length === 0) {
              const emptyValues = {
                dialCode: "",
                countryCode: "",
                number: "",
                numberWithCode: "",
              };
              handleValueChanged(emptyValues, fieldItem?.fieldName);
              return;
            }
            const updatedValues = {
              dialCode: country?.dialCode,
              countryCode: country?.iso2,
              number: sanitizedPhoneNumber,
              numberWithCode: sanitizedFullNumber,
            };
            handleValueChanged(updatedValues, fieldItem?.fieldName);
          }}
          errorMessage={errorMessage}
          defaultCountry={selectedValue?.countryCode}
          //@ts-ignore
          selectedValue={selectedValue?.number || ""}
          register={register(name, numberRules)}
          onBlur={() => trigger(name)}
          handleFlagChange={() => {
            const emptyValues = {
              dialCode: "",
              countryCode: "",
              number: "",
              numberWithCode: "",
            };
            handleValueChanged(emptyValues, fieldItem?.fieldName);
          }}
          placeholder={fieldItem?.placeholder}
          enableAutoDetection={enableAutoDetection}
          onAutoDetectionComplete={(countryCode: string, method: string) => {
            const autoDetectedValues = {
              dialCode: "",
              countryCode: countryCode,
              number: "",
              numberWithCode: "",
            };
            handleValueChanged(autoDetectedValues, fieldItem?.fieldName);
          }}
        />
        {errorMessage && (
          <span className="text-sm text-[red] h-0 mb-2">{errorMessage}</span>
        )}
      </div>
    );
  }
  if (
    (fieldType === "attachDocument" ||
      fieldType == "document" ||
      fieldType === "multiDocument") &&
    isVisibleWhen
  ) {
    const mandatoryRules = { ...fieldItem.rules };
    if (fieldItem?.mandatoryWhen) {
      let mandatoryWhen = fieldItem?.mandatoryWhen;
      if (
        mandatoryWhen &&
        mandatoryWhen?.condition == "notEqualTo" &&
        (watch(mandatoryWhen?.fieldName)?.value
          ? mandatoryWhen?.fieldValue.localeCompare(
              watch(mandatoryWhen?.fieldName)?.value,
            ) !== 0
          : mandatoryWhen?.fieldValue.localeCompare(
              watch(mandatoryWhen?.fieldName),
            ) !== 0)
      ) {
        mandatoryRules.required =
          watch(fieldItem?.fieldName)?.length > 0
            ? false
            : `${mandatoryWhen?.errorText}`;
      } else {
        mandatoryRules.required = false;
      }
    }

    return (
      <div className="w-full mb-5" id={name}>
        <FilePicker
          heading={label}
          isMandatory={mandatoryRules?.required || isMandatory}
          uploadFile={async (file: any) => await uploadDocs(file, name)}
          handleFileChange={(file: any) => handleValueChanged(file, "file")}
          key={label}
          documentType={fieldItem.documentType}
          handleDelete={handleDeleteFile}
          uploadedFiles={watch(name) || []}
          isMultiUpload={fieldItem?.type === "multiDocument"}
          watch={watch}
          setValue={setValue}
          register={register(
            name,
            selectedValue?.length > 0 ? {} : mandatoryRules,
          )}
          maxFiles={fieldItem?.maximumFiles}
          name={`file-${name}`}
          warning={fieldItem?.warning}
          displaySubInformation={fieldItem?.displaySubInformation}
          pdfLabel={fieldItem?.pdfLabel}
          allowedFileTypes={fieldItem?.allowedFileTypes}
          fieldItem={fieldItem}
          maxOcrReprocessCount={maxOcrReprocessCount}
          isOcrProcessDocument={isOcrReprocess}
        />
        {errorMessage && (
          <Label className="mt-5 text-red-500 text-sm mb-1">
            {errorMessage}
          </Label>
        )}
      </div>
    );
  }
  if (fieldType === "radioButton" && isVisibleWhen) {
    const transformedOptions = Array.isArray(options)
      ? typeof options[0] === "object"
        ? options.map((opt) => ({ value: opt.value, label: opt.label }))
        : options
      : options;

    if (fieldItem?.hideWhen) {
      const data = fieldItem.hideWhen?.every(
        (item: any) => watch(item.fieldName) === item.value,
      );
      if (data) {
        return null;
      }
    }

    return (
      <div
        className={`w-full mb-5 flex-col ${
          fieldItem.fieldName === "addMore" ? "hidden" : "flex"
        }`}
      >
        <FieldTitle
          label={label}
          isMandatory={isMandatory}
          htmlFor={`${"radio" + label}`}
          isPreview={fieldItem?.isPreview ? fieldItem?.isPreview : false}
          helpText={fieldItem?.helpText}
          hyperLinkText={fieldItem?.hyperLinkText}
          hyperLinkValue={fieldItem?.hyperLinkValue}
        />
        <RadioButton
          id={`${"radio" + label}`}
          errorMessage={errorMessage}
          handleChange={handleValueChanged}
          selectedValue={selectedValue}
          options={
            fieldItem.radiobuttonSourceType === "API"
              ? radioOptions
              : transformedOptions
          }
          isLoading={isRadioLoading}
          register={register(name, rules)}
        />
        {errorMessage && (
          <span className="text-sm text-[red] h-0 mb-2">{errorMessage}</span>
        )}
      </div>
    );
  }

  // if (
  //   (fieldType === "Button" || fieldType === "button") &&
  //   arrayIndex + 1 === totalCount &&
  //   isVisibleWhen
  // ) {
  //   return (
  //     <Button
  //       className="text-background rounded hover:bg-secondary hover:border-0 hover:text-background bg-secondary font-bold  text-sm px-5 py-2.5 mt-5 me-2 mb-2  "
  //       variant="outline"
  //       onClick={() => handleValueChanged(null)}
  //       type="submit"
  //     >
  //       {label}
  //     </Button>
  //   );
  // }

  if (fieldType === "monthYearPicker" && isVisibleWhen) {
    return (
      <div className="w-full mb-5 relative" id={name}>
        {displayNoTitle && (
          <FieldTitle
            label={label}
            isMandatory={isMandatory}
            helpText={fieldItem?.helpText}
            hyperLinkText={fieldItem?.hyperLinkText}
            hyperLinkValue={fieldItem?.hyperLinkValue}
          />
        )}

        <div
          className={`border rounded shadow-none ${
            errorMessage ? "border-error" : "border-border"
          } px-3 focus-visible:ring-1 p-1 [&_div]:w-full`}
        >
          <MonthPicker
            label={label}
            handleValueChanged={handleValueChanged}
            value={selectedValue}
            setError={setError}
            watch={watch}
            placeholder={fieldItem?.placeholder}
            clearErrors={clearErrors}
            register={register(name, rules)}
            fieldName={name}
            validateOn={fieldItem?.validateOn}
            validateWith={fieldItem?.validateWith}
            onBlur={() => trigger(name)}
          />
        </div>
        {errorMessage && (
          <span className="text-sm text-[red] h-0 mb-2">{errorMessage}</span>
        )}
      </div>
    );
  }

  if (fieldType === "yearPicker" && isVisibleWhen) {
    return (
      <div className="w-full mb-5 relative" id={name}>
        {displayNoTitle && (
          <FieldTitle
            label={label}
            isMandatory={isMandatory}
            helpText={fieldItem?.helpText}
            hyperLinkText={fieldItem?.hyperLinkText}
            hyperLinkValue={fieldItem?.hyperLinkValue}
          />
        )}

        <YearPicker
          label={label}
          handleValueChanged={handleValueChanged}
          value={selectedValue}
          setError={setError}
          watch={watch}
          clearErrors={clearErrors}
          register={register(name, rules)}
          fieldName={name}
          validateOn={fieldItem?.validateOn}
          validateWith={fieldItem?.validateWith}
          onBlur={() => trigger(name)}
        />

        {errorMessage && (
          <span className="text-sm text-[red] h-0 mb-2">{errorMessage}</span>
        )}
      </div>
    );
  }

  if (fieldType === "date" && isVisibleWhen) {
    let dateNow = null;
    if (fieldItem?.value == "currentDate") {
      dateNow = new Date().toISOString().split("T")[0];
    }
    let conditionallyDisabled = false;

    if (fieldItem?.defaultWhen) {
      if (
        isOcrReprocess &&
        watch("ocrReprocessCount") >= maxOcrReprocessCount &&
        !watch("isSuccessfullExtraction")
      ) {
        conditionallyDisabled = false;
      } else if (!watch(fieldItem?.defaultWhen?.fieldName)) {
        conditionallyDisabled = true;
      } else if (
        watch(fieldItem?.defaultWhen?.fieldName) ===
        fieldItem?.defaultWhen?.value
      ) {
        conditionallyDisabled = true;
      }
    }

    return (
      <div className="w-full mb-5 max-h-min " id={name}>
        <DatePicker
          label={label}
          errorMessage={errorMessage}
          isMandatory={isMandatory}
          field={fieldItem}
          handleChange={handleValueChanged}
          register={register(name, rules)}
          watch={watch}
          //@ts-ignore
          selectedValue={dateNow ?? selectedValue}
          placeholder={placeholder}
          handleTrigger={() => trigger(name)}
          disabled={fieldItem?.default || conditionallyDisabled}
          setError={setError}
          clearErrors={clearErrors}
          fieldName={name}
          validateWith={fieldItem?.validateWith}
          validateOn={fieldItem?.validateOn}
        />
        {
          <span className="text-xs text-gray-800">
            {fieldItem?.displaySubInformation}
          </span>
        }
        {errorMessage && (
          <span className="text-sm text-[red] h-0 mb-2">{errorMessage}</span>
        )}
      </div>
    );
  }
  if (fieldType == "multiPickList" && isVisibleWhen) {
    let mandatoryRules: any = { ...rules };
    if (fieldItem?.mandatoryWhen) {
      let mandatoryWhen = fieldItem?.mandatoryWhen;

      if (
        mandatoryWhen &&
        mandatoryWhen?.condition == "greaterThan" &&
        mandatoryWhen?.fieldValue <= watch(mandatoryWhen?.fieldName)
      ) {
        mandatoryRules = {
          required:
            watch(fieldItem?.fieldName)?.length > 0
              ? false
              : `${mandatoryWhen?.errorText}`,
        };
      }
    }
    return (
      <div className="w-full mb-5" id={name}>
        {displayNoTitle && (
          <FieldTitle
            label={label}
            isMandatory={mandatoryRules?.required || isMandatory}
            helpText={fieldItem?.helpText}
            hyperLinkText={fieldItem?.hyperLinkText}
            hyperLinkValue={fieldItem?.hyperLinkValue}
          />
        )}
        {fieldItem?.customText && (
          <span className="text-xs text-black mb-2 block">
            {fieldItem.customText}
          </span>
        )}
        <DropDownBox
          selectedValue={selectedValue}
          errorMessage={errorMessage}
          placeholder={placeholder}
          items={lookupOptions}
          onChange={(selectedValue: any) => {
            handleValueChanged(selectedValue);
          }}
          register={
            (fromApplicationFilter ? enableValidation : true) &&
            register(name, mandatoryRules)
          }
          onBlur={() => trigger(name)}
          isLoading={isLoading}
          isMulti={fieldType === "multiPickList"}
          onFocus={() => {
            setIsDropDownLoading(true);
            fetchOptionsData();
          }}
        />
        {errorMessage && (
          <span className="text-sm text-[red] h-0 mb-2">{errorMessage}</span>
        )}
      </div>
    );
  }

  if ((fieldType === "pickList" || fieldType === "dropdown") && isVisibleWhen) {
    let conditionallyDisabled = false;

    if (fieldItem?.defaultWhen) {
      if (
        isOcrReprocess &&
        watch("ocrReprocessCount") >= maxOcrReprocessCount &&
        !watch("isSuccessfullExtraction")
      ) {
        conditionallyDisabled = false;
      } else if (!watch(fieldItem?.defaultWhen?.fieldName)) {
        conditionallyDisabled = true;
      } else if (
        watch(fieldItem?.defaultWhen?.fieldName) ===
        fieldItem?.defaultWhen?.value
      ) {
        conditionallyDisabled = true;
      }
    }

    if (fieldItem?.displayNameType === "markup") {
      let nameLabel = fieldItem?.markupDisplayName
        ?.replaceAll("<FirstName>", `${watchedFields["firstName"]} `)
        ?.replaceAll("<LastName>", ` ${watchedFields["lastName"]}`);

      return (
        <div className="w-full mb-3" id={name}>
          <div className="prose dark:prose-invert max-w-none mb-6 text-sm">
            <ReactMarkdown
              className={`markDown my--4  ${isMandatory ? "" : ""}`}
              remarkPlugins={[remarkGfm]}
              rehypePlugins={[rehypeRaw]}
            >
              {replacePlaceholders(nameLabel, studentData)}
            </ReactMarkdown>
            {isMandatory && (
              <span className="text-sm -mt-1 text-red-500">*</span>
            )}
          </div>
          {fieldItem?.customText && (
            <span className="text-xs text-black mb-2 block">
              {fieldItem.customText}
            </span>
          )}
          <DropDownBox
            //@ts-ignore
            selectedValue={selectedValue}
            noOptionsMessage={fieldItem?.noOptionsMessage}
            errorMessage={errorMessage}
            placeholder={placeholder}
            validateWithStart={fieldItem?.validateWithStart}
            validateWithEnd={fieldItem?.validateWithEnd}
            setError={setError}
            fieldName={name}
            clearErrors={clearErrors}
            subSectionIndex={subSectionIndex}
            watch={watch}
            validationMessageForEnd={fieldItem?.validationMessageForEnd}
            validationMessageForStart={fieldItem?.validationMessageForStart}
            items={fromApplicationFilter ? getLookupData : lookupOptions}
            onChange={(selectedValue: any) => {
              handleValueChanged(selectedValue);
            }}
            validateWithFieldName={fieldItem.validateWithFieldName}
            register={
              (fromApplicationFilter ? enableValidation : true) &&
              register(name, rules)
            }
            onBlur={() => trigger(name)}
            isLoading={isLoading || isDropDownLoading}
            isDisabled={disabled || conditionallyDisabled}
            onFocus={() => {
              setIsDropDownLoading(true);
              fetchOptionsData();
            }}
          />
          {errorMessage && (
            <span className="text-sm text-[red] h-0 mb-2">{errorMessage}</span>
          )}
        </div>
      );
    }
    let nameLabel = label
      ?.replaceAll("<firstName>", `${watchedFields["firstName"]} `)
      ?.replaceAll("<lastName>", ` ${watchedFields["lastName"]}`);

    return (
      <div className="w-full mb-5" id={name}>
        {displayNoTitle && (
          <div className="flex justify-between">
            <FieldTitle
              label={nameLabel}
              isMandatory={isMandatory}
              helpText={fieldItem?.helpText}
              hyperLinkText={fieldItem?.hyperLinkText}
              hyperLinkValue={fieldItem?.hyperLinkValue}
            />
            {fieldItem?.linkText &&
              watch(
                `${fieldItem.subSectionName}.${subSectionIndex}.${fieldItem.validateWithFieldName}`,
              ) && (
                <button
                  type="button"
                  className="text-sm underline text-on-background"
                  onClick={() => {
                    setValue(
                      `${fieldItem.subSectionName}.${subSectionIndex}.${fieldItem.linkTextFieldName}`,
                      fieldItem?.linkTextFieldValue,
                    );
                    if (fieldItem.linkTextDepentdentFieldValue) {
                      setValue(
                        `${fieldItem.subSectionName}.${subSectionIndex}.${fieldItem.fieldName}`,
                        fieldItem.morePickListValues[0],
                      );
                      setValue(
                        `${fieldItem.subSectionName}.${subSectionIndex}.${fieldItem.fieldName}DisplayName`,
                        fieldItem.morePickListValues[0].label,
                      );
                    }
                  }}
                >
                  {fieldItem.linkText}
                </button>
              )}
          </div>
        )}
        {fieldItem?.customText && (
          <span className="text-xs text-black mb-2 block">
            {fieldItem.customText}
          </span>
        )}
        <DropDownBox
          //@ts-ignore
          selectedValue={selectedValue}
          errorMessage={errorMessage}
          validateWithStart={fieldItem?.validateWithStart}
          noOptionsMessage={fieldItem?.noOptionsMessage}
          validateWithEnd={fieldItem?.validateWithEnd}
          fieldName={name}
          validationMessageForEnd={fieldItem?.validationMessageForEnd}
          validationMessageForStart={fieldItem?.validationMessageForStart}
          setError={setError}
          placeholder={placeholder}
          watch={watch}
          items={
            fromApplicationFilter
              ? getLookupData
              : fieldItem.hasMorePickListValues
                ? lookupOptions?.concat(fieldItem.morePickListValues)
                : lookupOptions
          }
          onChange={(selectedValue: any) => {
            handleValueChanged(selectedValue);
          }}
          register={
            (fromApplicationFilter ? enableValidation : true) &&
            register(name, rules)
          }
          onBlur={() => trigger(name)}
          isLoading={isLoading || isDropDownLoading}
          onFocus={() => {
            setIsDropDownLoading(true);
            fetchOptionsData();
          }}
          subsectionName={fieldItem.subSectionName}
          subSectionIndex={subSectionIndex}
          validateWithFieldName={fieldItem.validateWithFieldName}
          isDisabled={disabled || conditionallyDisabled}
          isFilteredField={filterQualify}
          filterRules={filterRules}
          selectedAnswers={qualifyingQuestionsState}
        />
        <span className="text-xs text-gray-800">
          {fieldItem?.displaySubInformation}
        </span>
        {errorMessage && (
          <span className="text-sm text-[red] h-0 mb-2">{errorMessage}</span>
        )}
        <TextWithHyperlink
          text={fieldItem?.text}
          hyperLinkText={fieldItem?.hyperLinkText}
          hyperLinkValue={fieldItem?.hyperLinkValue}
          className="text-xs text-gray-800"
        />
      </div>
    );
  }
  if (
    (fieldType === "textField" ||
      fieldType === "text" ||
      fieldType === "email") &&
    isVisibleWhen
  ) {
    let conditionallyDisabled = false;

    if (fieldItem?.defaultWhen) {
      if (
        isOcrReprocess &&
        watch("ocrReprocessCount") >= maxOcrReprocessCount &&
        !watch("isSuccessfullExtraction")
      ) {
        conditionallyDisabled = false;
      } else if (!watch(fieldItem?.defaultWhen?.fieldName)) {
        conditionallyDisabled = true;
      } else if (
        watch(fieldItem?.defaultWhen?.fieldName) ===
        fieldItem?.defaultWhen?.value
      ) {
        conditionallyDisabled = true;
      }
    }
    const showCharacterCount =
      fieldItem?.showCharacterCount && fieldItem?.limit;
    const limitNote = fieldItem?.subTitle;
    return (
      <div className={`${showCharacterCount ? "mb-0" : "mb-5"}`} id={name}>
        <div className="flex items-center mb-1">
          {displayNoTitle && (
            <FieldTitle
              label={label}
              isMandatory={isMandatory}
              helpText={fieldItem?.helpText}
              hyperLinkText={fieldItem?.hyperLinkText}
              hyperLinkValue={fieldItem?.hyperLinkValue}
            />
          )}
          {limitNote && (
            <span className="text-xs text-gray-500 ml-1">{limitNote}</span>
          )}
        </div>
        {fieldItem?.customText && (
          <span className="text-xs text-black mb-2 block">
            {fieldItem.customText}
          </span>
        )}
        <Input
          className={`border rounded shadow-none ${
            errorMessage ? "border-error" : "border-border"
          }  px-3 focus-visible:ring-1`}
          //@ts-ignore
          value={selectedValue}
          disabled={fieldItem?.default || conditionallyDisabled}
          onInput={(e: any) => {
            if (fieldType === "email") {
              handleValueChanged(e.target.value.replace(/\s/g, ""));
            } else handleValueChanged(e.target.value);
          }}
          placeholder={placeholder}
          maxLength={fieldItem?.limit}
          onBlurCapture={() => trigger(name)}
          type={fieldItem?.inputType}
          {...register(name, rules)}
        />
        <span className="text-xs text-gray-800">
          {fieldItem?.displaySubInformation}
        </span>
        {(showCharacterCount || errorMessage) && (
          <div className="flex justify-between mt-1 text-xs">
            {errorMessage && (
              <span className="text-sm text-[red] h-0 mb-2">
                {errorMessage}
              </span>
            )}
            {showCharacterCount && (
              <span className="text-gray-500 text-sm ml-auto ">
                {selectedValue ? selectedValue.length : 0}/{fieldItem?.limit}
              </span>
            )}
          </div>
        )}
        <TextWithHyperlink
          text={fieldItem?.text}
          hyperLinkText={fieldItem?.hyperLinkText}
          hyperLinkValue={fieldItem?.hyperLinkValue}
          className="text-xs text-gray-800"
        />
      </div>
    );
  }

  if (fieldType == "multilineText" && isVisibleWhen) {
    // Word count functionality
    const countWords = (text: string): number => {
      if (!text || text.trim() === "") return 0;
      return text.trim().split(/\s+/).length;
    };

    const currentWordCount = countWords(selectedValue || "");
    const maxWords = fieldItem?.maxWords;
    const wordsRemaining = maxWords ? maxWords - currentWordCount : null;
    const isWordLimitExceeded = maxWords && currentWordCount > maxWords;

    const handleTextChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
      const newValue = e.target.value;

      if (maxWords) {
        const wordCount = countWords(newValue);
        // If word count exceeds limit, prevent the change
        if (wordCount > maxWords) {
          e.preventDefault();
          return;
        }
      }
      handleValueChanged(newValue);
    };

    const handleInput = (e: React.FormEvent<HTMLTextAreaElement>) => {
      const target = e.target as HTMLTextAreaElement;
      const newValue = target.value;

      if (maxWords) {
        const wordCount = countWords(newValue);
        if (wordCount > maxWords) {
          // Restore the previous value and prevent the input
          target.value = selectedValue || "";
          e.preventDefault();
          return;
        }
      }
      handleValueChanged(newValue);
    };

    const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
      if (maxWords) {
        const target = e.target as HTMLTextAreaElement;
        const currentValue = target.value;
        const currentWordCount = countWords(currentValue);
        const key = e.key;

        // Only block if we're at word limit and trying to add a space or enter (new word)
        if (currentWordCount >= maxWords && (key === " " || key === "Enter")) {
          e.preventDefault();
          return;
        }

        // For other keys, let the input handler check the final word count
      }
    };

    const handlePaste = (e: React.ClipboardEvent<HTMLTextAreaElement>) => {
      if (maxWords) {
        e.preventDefault();
        const pastedText = e.clipboardData.getData("text");
        const currentText = selectedValue || "";
        const textArea = e.target as HTMLTextAreaElement;
        const start = textArea.selectionStart;
        const end = textArea.selectionEnd;

        // Calculate the new text after paste
        const newText =
          currentText.substring(0, start) +
          pastedText +
          currentText.substring(end);
        const newWordCount = countWords(newText);

        if (newWordCount <= maxWords) {
          handleValueChanged(newText);
          // Update the textarea value manually since we prevented default
          textArea.value = newText;
        }
        // If word count would exceed limit, do nothing (paste is blocked)
        return;
      }
    };

    // Create validation rules with word count validation
    const textRules = {
      ...rules,
      ...(maxWords && {
        validate: {
          ...rules?.validate,
          wordCount: (value: string) => {
            if (!value) return true; // Allow empty if not required
            const wordCount = countWords(value);
            return (
              wordCount <= maxWords ||
              `Maximum ${maxWords} words allowed. Current: ${wordCount} words.`
            );
          },
        },
      }),
    };

    return (
      <div className="mb-5" id={name}>
        {displayNoTitle && (
          <FieldTitle
            label={label}
            isMandatory={isMandatory}
            helpText={fieldItem?.helpText}
            hyperLinkText={fieldItem?.hyperLinkText}
            hyperLinkValue={fieldItem?.hyperLinkValue}
          />
        )}
        <Textarea
          placeholder={placeholder || "Type your message here."}
          id={`id-${label}`}
          className={`border rounded shadow-none ${
            errorMessage || isWordLimitExceeded
              ? "border-error"
              : "border-border"
          }  px-3 focus-visible:ring-1  placeholder:text-slate-400`}
          value={selectedValue}
          disabled={fieldItem?.default}
          onChange={handleTextChange}
          onInput={handleInput}
          onKeyDown={handleKeyDown}
          onPaste={handlePaste}
          onBlurCapture={() => trigger(name)}
          maxLength={fieldItem?.limit}
          {...register(name, textRules)}
        />

        {/* Word count display */}
        {maxWords && (
          <div className="mt-2 text-sm">
            <span
              className={
                wordsRemaining !== null && wordsRemaining <= 0
                  ? "text-red-500"
                  : wordsRemaining !== null &&
                      wordsRemaining < Math.ceil(maxWords * 0.2)
                    ? "text-amber-600"
                    : "text-gray-600"
              }
            >
              {wordsRemaining !== null && wordsRemaining >= 0
                ? `${wordsRemaining} word${wordsRemaining !== 1 ? "s" : ""} remaining`
                : `${currentWordCount}/${maxWords} words`}
            </span>
          </div>
        )}

        {errorMessage && (
          <span className="text-sm text-[red] h-0 mb-2">{errorMessage}</span>
        )}
      </div>
    );
  }

  if (fieldType == "multiCheckbox" && isVisibleWhen) {
    const { checkboxes, options } = fieldItem;
    return (
      <div id={name}>
        <FieldTitle
          label={label}
          isMandatory={isMandatory}
          helpText={fieldItem?.helpText}
          hyperLinkText={fieldItem?.hyperLinkText}
          hyperLinkValue={fieldItem?.hyperLinkValue}
        />

        {/* Support for checkboxes array (existing format) */}
        {checkboxes &&
          checkboxes.map((checkBoxItem: any, index: number) => (
            <>
              {checkBoxItem?.displayName && (
                <FieldTitle
                  label={checkBoxItem?.displayName || checkBoxItem?.label}
                  helpText={checkBoxItem?.helpText}
                  hyperLinkText={checkBoxItem?.hyperLinkText}
                  hyperLinkValue={checkBoxItem?.hyperLinkValue}
                />
              )}

              <CheckBox
                label={checkBoxItem?.displayName || checkBoxItem?.label}
                handleChange={handleValueChanged}
                fieldItem={checkBoxItem}
                // disabled={fieldItem?.default}
              />
            </>
          ))}

        {/* Support for options array (simplified format) */}
        {options && !checkboxes && (
          <div className="ml-4 mb-5">
            {options.map((option: string, index: number) => (
              <CheckBox
                key={index}
                label={option}
                compact={true}
                handleChange={(checked: boolean) => {
                  // Handle the value change for options format
                  const currentValues = selectedValue || [];
                  let newValues;

                  if (checked) {
                    // Add the option if checked
                    newValues = [...currentValues, option];
                  } else {
                    // Remove the option if unchecked
                    newValues = currentValues.filter(
                      (value: string) => value !== option,
                    );
                  }

                  handleValueChanged(newValues);
                }}
                selectedValue={
                  selectedValue && Array.isArray(selectedValue)
                    ? selectedValue.includes(option)
                    : false
                }
                fieldItem={{
                  fieldName: `${fieldItem.fieldName}_${index}`,
                  value: option,
                }}
              />
            ))}
          </div>
        )}
      </div>
    );
  }

  if (fieldType === "checkbox" && isVisibleWhen) {
    if (fieldItem?.default)
      return (
        <div id={name}>
          {fieldItem?.displayName && (
            <FieldTitle
              label={fieldItem?.displayName}
              isMandatory={isMandatory}
              helpText={fieldItem?.helpText}
              hyperLinkText={fieldItem?.hyperLinkText}
              hyperLinkValue={fieldItem?.hyperLinkValue}
            />
          )}

          <CheckBox
            selectedValue={fieldItem?.defaultFieldValue}
            label={label}
            isMandatory={isMandatory}
            handleChange={handleValueChanged}
            fieldItem={fieldItem}
            disabled={fieldItem?.default}
            markdownText={fieldItem?.markdownText}
          />
        </div>
      );
    let defaultValue = null,
      disabled = false;
    if (fieldItem?.defaultWhen) {
      let defaultWhenObj = fieldItem?.defaultWhen,
        expression = defaultWhenObj?.condition;

      disabled = defaultWhenObj?.disabled;
      if (
        expression == "greaterThan" &&
        defaultWhenObj?.fieldValue <= watch(defaultWhenObj?.fieldName)
      ) {
        defaultValue = true;
      }
      if (
        expression == "strictlyGreaterThan" &&
        defaultWhenObj?.fieldValue < watch(defaultWhenObj?.fieldName)
      ) {
        defaultValue = true;
      }
    }

    if (
      fieldItem?.fieldName === fieldItem?.disableWhen?.fieldNameToDisable &&
      !watch(fieldItem?.disableWhen?.fieldName)
    ) {
      disabled = true;
    }
    return (
      <div id={name}>
        {fieldItem?.displayName && (
          <FieldTitle
            label={fieldItem?.displayName}
            isMandatory={isMandatory}
            helpText={fieldItem?.helpText}
            hyperLinkText={fieldItem?.hyperLinkText}
            hyperLinkValue={fieldItem?.hyperLinkValue}
          />
        )}

        <CheckBox
          selectedValue={
            selectedValue == "on" ||
            selectedValue ||
            fieldItem?.defaultFieldValue ||
            defaultValue
          }
          label={label}
          register={register(name, rules)}
          isMandatory={isMandatory}
          errorMessage={errorMessage}
          handleChange={handleValueChanged}
          fieldItem={fieldItem}
          disabled={fieldItem?.default || disabled}
          markdownText={fieldItem?.markdownText}
        />
      </div>
    );
  }

  if (fieldType === "staticTable" && isVisibleWhen) {
    const sortingOrder: any = fieldItem?.sortOrder;

    table.sort((a: any, b: any) => {
      for (let key of sortingOrder) {
        if (a[key] !== b[key]) {
          return sortingOrder.indexOf(a[key]) - sortingOrder.indexOf(b[key]);
        }
      }
      return 0;
    });

    return (
      <Table className="border mb-5" style={{ borderRadius: 8 }}>
        <TableHeader>
          <TableRow>
            {sortingOrder.map((key: any) => {
              return <TableCell key={key}>{key}</TableCell>;
            })}
          </TableRow>
        </TableHeader>
        <TableBody>
          {table.map((item: any, index: any) => {
            const isOptionsRepeating =
              index > 0 && table[index - 1].Options === item.Options;

            const isEmptyRow = Object.values(item).every((value) => !value);

            return (
              <TableRow
                key={index}
                style={{
                  borderTop: isEmptyRow ? "none" : "1px solid #e2e8f0",
                }}
              >
                {item?.hasOwnProperty("Options") && !isOptionsRepeating && (
                  <TableCell
                    rowSpan={
                      table.filter((i: any) => i.Options === item.Options)
                        .length
                    }
                  >
                    {item.Options}
                  </TableCell>
                )}

                {sortingOrder.map((key: any) => {
                  if (key !== "Options") {
                    return <TableCell key={key}>{item[key]}</TableCell>;
                  }
                  return null;
                })}
              </TableRow>
            );
          })}
        </TableBody>
      </Table>
    );
  }

  if (fieldType === "alertInformation" && isVisibleWhen) {
    return (
      <div className="flex flex-row gap-x-2 mb-4">
        <div>
          <AlertCircle
            name="shield-alert"
            height={20}
            width={20}
            color={"var(--color-surface)"}
          />
        </div>
        <div className="max-w-none text-sm">
          {fieldItem?.hyperLinkText && fieldItem?.hyperLinkValue ? (
            <TextWithHyperlink
              text={replacePlaceholders(fieldItem?.text, studentData)}
              hyperLinkText={fieldItem?.hyperLinkText}
              hyperLinkValue={fieldItem?.hyperLinkValue}
            />
          ) : (
            <ReactMarkdown
              className="markDown"
              components={{ a: LinkRenderer }}
              remarkPlugins={[remarkGfm]}
              rehypePlugins={[rehypeRaw]}
            >
              {replacePlaceholders(fieldItem?.text, studentData)}
            </ReactMarkdown>
          )}
        </div>
      </div>
    );
  }

  if (fieldType === "displayInformation" && isVisibleWhen) {
    let nameLabel = fieldItem?.text
      ?.replaceAll("<firstName>", `${watchedFields["firstName"]} `)
      ?.replaceAll("<lastName>", ` ${watchedFields["lastName"]}`);
    if (fieldItem?.hideWhen) {
      const data = fieldItem.hideWhen?.every(
        (item: any) => watch(item.fieldName) === item.value,
      );
      if (data) {
        return null;
      }
    }
    return (
      <div className={`mb-5 items-center`}>
        <div
          className="prose dark:prose-invert max-w-none mb-6 text-sm"
          style={getBrandSpecificFontStyle(fontSize, "label")}
        >
          {fieldItem?.hyperLinkText && fieldItem?.hyperLinkValue ? (
            <TextWithHyperlink
              text={nameLabel}
              hyperLinkText={fieldItem?.hyperLinkText}
              hyperLinkValue={fieldItem?.hyperLinkValue}
            />
          ) : (
            <ReactMarkdown
              className="markDown my-4"
              components={{ a: LinkRenderer }}
              remarkPlugins={[remarkGfm]}
              rehypePlugins={[rehypeRaw]}
            >
              {replacePlaceholders(nameLabel, studentData)}
            </ReactMarkdown>
          )}
        </div>
      </div>
    );
  }

  if (fieldType === "subsection" && sectionCanRepeat && isVisibleWhen) {
    let fieldData = fieldItem?.[fieldItem?.subSection]?.fieldData;

    return (
      <div className="w-full mb-5 form-section rounded-xl border bg-card text-card-foreground shadow p-5 ">
        <div className="flex justify-between">
          <Label className="mb-4 flex text-base font-bold ">
            {`${fieldItem?.displayName} ${typeof subSectionIndex === "number" ? subSectionIndex + 1 : 1}`}
          </Label>
          {fieldItem?.minLength <=
            (typeof subSectionIndex === "number" ? subSectionIndex : 0) && (
            <div
              onClick={() => {
                handleRemove(subSectionIndex, fieldItem);
              }}
              className="cursor-pointer"
            >
              <Trash2 height={20} width={20} />
            </div>
          )}
        </div>

        {fieldData?.map((item: any, i: number) => {
          return (
            <DynamicFields
              {...rest}
              {...item}
              key={i}
              fieldItem={item}
              register={register}
              watch={watch}
              errorMessage={
                errors?.[fieldItem?.subSection]?.[subSectionIndex]?.[
                  item?.fieldName
                ]?.message ||
                errors?.[fieldItem?.subSection]?.[subSectionIndex]?.[
                  `${item?.documentType}`
                ]?.message
              }
              selectedValue={
                watch(
                  `${fieldItem?.fieldName}.${subSectionIndex}.${item?.fieldName}`,
                ) ||
                watch(`${item?.documentType}`) ||
                ""
              }
              handleValueChanged={(value: any, type?: string) => {
                clearErrors(
                  `${fieldItem?.fieldName}.${subSectionIndex}.${item?.fieldName}`,
                );
                if (type === "file") {
                  setValue(
                    `${fieldItem?.fieldName}.${subSectionIndex}.${item?.fieldName}`,
                    value,
                  );
                  return;
                }
                if (item?.type === "pickList" && item?.fieldDisplayName) {
                  setValue(
                    `${fieldItem?.fieldName}.${subSectionIndex}.${item?.fieldDisplayName}`,
                    value?.label,
                  );
                  setValue(
                    `${fieldItem?.fieldName}.${subSectionIndex}.${item?.fieldName}`,
                    value,
                  );
                } else {
                  setValue(
                    `${fieldItem?.fieldName}.${subSectionIndex}.${item?.fieldName}`,
                    value,
                  );
                }
                if (item?.resetChild) {
                  if (Array.isArray(item?.resetChild)) {
                    for (const resetItem of item?.resetChild) {
                      if (resetItem) {
                        setValue(
                          `${fieldItem?.fieldName}.${subSectionIndex}.${resetItem}`,
                          "",
                        );
                        setValue(
                          `${fieldItem?.fieldName}.${subSectionIndex}.${resetItem}DisplayName`,
                          "",
                        );
                        clearErrors(
                          `${fieldItem?.fieldName}.${subSectionIndex}.${resetItem}`,
                        );
                      }
                    }
                  } else {
                    setValue(
                      `${fieldItem?.fieldName}.${subSectionIndex}.${item?.resetChild}`,
                      "",
                    );
                    setValue(
                      `${fieldItem?.fieldName}.${subSectionIndex}.${item?.resetChild}DisplayName`,
                      "",
                    );
                    clearErrors(
                      `${fieldItem?.fieldName}.${subSectionIndex}.${item?.resetChild}`,
                    );
                  }
                }
              }}
              label={
                item?.text ||
                item?.label ||
                item?.displayName ||
                item?.placeholder
              }
              name={`${fieldItem?.fieldName}.${subSectionIndex}.${item?.fieldName}`}
              setValue={setValue}
              isVisibleWhen={checkVisibility(
                item?.visibleWhen,
                fieldItem?.fieldName,
                subSectionIndex,
              )}
              subSectionIndex={subSectionIndex}
              clearErrors={clearErrors}
              setError={setError}
              trigger={trigger}
              handleDeleteFile={handleDeleteFile}
              uploadDocs={uploadDocs}
            />
          );
        })}
      </div>
    );
  }
  if (fieldType === "subsection" && isVisibleWhen) {
    let fieldData = fieldItem?.[fieldItem?.subSection]?.fieldData;

    return (
      <div className="w-full mb-5 form-section rounded-xl border bg-card text-card-foreground shadow p-5 ">
        <Label
          className="mb-4 flex text-base font-bold "
          style={getBrandSpecificFontStyle(fontSize, "label")}
        >
          {fieldItem?.displayName}
        </Label>
        {fieldData?.map((item: any, i: number) => {
          return (
            <DynamicFields
              {...rest}
              {...item}
              key={i}
              fieldItem={item}
              register={register}
              watch={watch}
              errorMessage={
                errors?.[item?.fieldName]?.message ||
                errors?.[`${item?.documentType}`]?.message
              }
              selectedValue={
                watch(item?.fieldName) || watch(`${item?.documentType}`) || ""
              }
              handleValueChanged={(value: any, type?: string) => {
                clearErrors(item?.fieldName);
                if (item?.dependentWhen) {
                  let dependentWhen = item?.dependentWhen;
                  if (value == dependentWhen?.value) {
                    fieldData.map((ele: any, index: number) => {
                      if (ele?.dependentWhen && i !== index) {
                        let dependentWhenObj = ele?.dependentWhen;
                        setValue(
                          ele?.fieldName,
                          watch(dependentWhenObj?.dependentFieldValueFrom),
                        );

                        if (dependentWhenObj?.dependentFieldDisplayValueFrom) {
                          setValue(
                            ele?.fieldDisplayName,
                            watch(
                              dependentWhenObj?.dependentFieldDisplayValueFrom,
                            ),
                          );
                        }
                      }
                    });
                  } else if (value == "No") {
                    fieldData.map((ele: any, index: number) => {
                      if (ele?.dependentWhen && i !== index) {
                        let dependentWhenObj = ele?.dependentWhen;
                        setValue(ele?.fieldName, "");
                        if (dependentWhenObj?.dependentFieldDisplayValueFrom) {
                          setValue(ele?.fieldDisplayName, "");
                        }
                      }
                    });
                  }
                }
                if (type === "file") {
                  setValue(item.fieldName, value);
                  return;
                }

                if (item.type === "pickList" && item?.fieldDisplayName) {
                  setValue(
                    item?.fieldDisplayName,
                    typeof value === "object" ? value.label : value,
                  );
                }
                if (item?.resetChild) {
                  if (Array.isArray(item.resetChild)) {
                    item?.resetChild.forEach((child: any) => {
                      setValue(child, "");
                      setValue(`${child}DisplayName`, "");
                      clearErrors(child);
                    });
                  } else {
                    setValue(item?.resetChild, "");
                    setValue(`${item?.resetChild}DisplayName`, "");
                    clearErrors(item?.resetChild);
                  }
                }
                setValue(item?.fieldName, value);
              }}
              label={item?.label || item?.displayName || item?.placeholder}
              name={item?.fieldName}
              setValue={setValue}
              isVisibleWhen={checkVisibility(item?.visibleWhen)}
              clearErrors={clearErrors}
              trigger={trigger}
              setError={setError}
              uploadDocs={uploadDocs}
              handleDeleteFile={handleDeleteFile}
            />
          );
        })}
      </div>
    );
  }

  if (fieldType === "signature" && isVisibleWhen) {
    return (
      <Signature
        displayName={label}
        handleFileChange={(file: any) => handleValueChanged(file, "file")}
        uploadedFiles={watch(name)}
        handleDeleteFile={handleDeleteFile}
        isMandatory={fieldItem?.mandatory || isMandatory}
      />
    );
  }
  if (fieldType === "editDataAlert" && isVisibleWhen) {
    const currentValue = watch(fieldItem?.fieldName);
    if (
      (!currentValue && isOcrReprocess) ||
      (currentValue === fieldItem?.defaultValue && isOcrReprocess)
    ) {
      return (
        <div
          className="w-full mb-5 border-2 border-yellow-400 bg-[#F8FAFC] rounded-xl p-6 flex flex-col gap-3"
          id={name}
          style={{ boxSizing: "border-box" }}
        >
          <div className="flex flex-row items-start gap-5">
            <AlertTriangle
              name="shield-alert"
              height={28}
              width={28}
              color="#eab308"
              style={{ minWidth: 32 }}
            />
            <div className="">
              {fieldItem?.hyperLinkText && fieldItem?.hyperLinkValue ? (
                <TextWithHyperlink
                  text={fieldItem?.text}
                  hyperLinkText={fieldItem?.hyperLinkText}
                  hyperLinkValue={fieldItem?.hyperLinkValue}
                />
              ) : (
                <ReactMarkdown
                  className="markDown"
                  components={{
                    span: ({ className, ...props }) =>
                      className === "required-star" && isMandatory ? (
                        <span className="text-sm text-error">*</span>
                      ) : (
                        <span {...props} />
                      ),
                  }}
                  remarkPlugins={[remarkGfm]}
                  rehypePlugins={[rehypeRaw]}
                >
                  {fieldItem?.text}
                </ReactMarkdown>
              )}
            </div>
          </div>
          <div className="flex justify-start ml-12">
            <RadioButton
              id={`editDataAlert-radio-${name}`}
              options={fieldItem?.options?.map((opt: string) => ({
                value: opt,
                label: opt,
              }))}
              selectedValue={currentValue}
              errorMessage={errorMessage}
              handleChange={(value: string) => {
                setValue(fieldItem?.fieldName, value);
                if (value === "No" && fieldItem?.resetChild) {
                  if (Array.isArray(fieldItem.resetChild)) {
                    fieldItem.resetChild.forEach((child: string) => {
                      setValue(child, false);
                    });
                  }
                }
              }}
              register={register(fieldItem?.fieldName)}
            />
          </div>
        </div>
      );
    } else if (currentValue === "No") {
      return (
        <p className="text-sm text-[#26960A] font-extrabold pb-4">
          {fieldItem?.editText}
        </p>
      );
    }
    return null;
  }

  if (fieldType === "preview") {
    const response = JSON.parse(localStorage.getItem("sections") ?? "[]");

    // Filter sections that should be shown in preview
    const visibleSections = sortOrder(response, "displayOrder")?.filter(
      (item: any) => {
        // Exclude "Review & Submit" section
        if (item?.displayName === "Review & Submit") {
          return false;
        }

        // If dontShowInPreview field exists, respect its value
        if (item?.dontShowInPreview !== undefined) {
          return !item?.dontShowInPreview;
        }

        // If dontShowInPreview doesn't exist, show section (default behavior)
        return true;
      },
    );

    return (
      <div className={`mb-5 items-center w-full`}>
        {visibleSections?.map((item: any, visibleIndex: any) => {
          return (
            <div key={visibleIndex} className="pb-4">
              {visibleIndex !== 0 && <Separator className="my-2" />}
              <FieldTitle
                label={item?.displayName}
                key={visibleIndex}
                isPreview={true}
                hyperLinkText={item?.hyperLinkText}
                hyperLinkValue={item?.hyperLinkValue}
              />

              {item?.fieldData?.map((field: any, i: any) => {
                if (
                  field?.type !== "button" &&
                  field?.type !== "displayInformation" &&
                  field?.type !== "staticTable" &&
                  field?.type !== "subsection" &&
                  field?.type !== "header" &&
                  field?.type !== "preview" &&
                  field?.type !== "canShowPreview" &&
                  checkVisibilityForPreview(field?.visibleWhen, studentData)
                ) {
                  return (
                    <div key={i} className="py-2">
                      <div className="flex items-start  gap-2">
                        {(field?.type === "document" ||
                          field?.type === "multiDocument" ||
                          field?.type === "checkbox" ||
                          field?.type === "signature") &&
                          showOnly(field) && (
                            <Image
                              priority
                              src={
                                studentData[field?.fieldName] === false ||
                                studentData[field?.fieldName] === undefined
                                  ? uncheck
                                  : check
                              }
                              height={20}
                              width={20}
                              alt="Follow us on Twitter"
                            />
                          )}
                        {showOnly(field) && (
                          <p
                            className="text-sm"
                            style={getBrandSpecificFontStyle(
                              fontSize,
                              "paragraph-text",
                            )}
                          >
                            {renderInlineWithLinks(
                              field?.displayName || field?.label || "",
                            )}
                            {field?.type !== "displayInformation" &&
                              field?.type !== "header" &&
                              field?.type !== "document" &&
                              field?.type !== "checkbox" &&
                              field?.type !== "preview" &&
                              field?.type !== "multiPickList" &&
                              field?.type !== "multiCheckbox" &&
                              field?.type !== "date" && (
                                <span className="text-sm font-bold">
                                  :{" "}
                                  {renderInlineWithLinks(showOnly(field) || "")}
                                </span>
                              )}
                            {field?.type === "multiPickList" && (
                              <span className="text-sm font-bold">
                                {" : "}
                                {renderInlineWithLinks(
                                  studentData?.[field?.fieldName]
                                    ?.map((obj: any) => obj.value)
                                    .join(", ") || " ",
                                )}
                              </span>
                            )}
                            {field?.type === "multiCheckbox" && (
                              <span className="text-sm font-bold">
                                {" : "}
                                {renderInlineWithLinks(
                                  (() => {
                                    const fieldData =
                                      studentData?.[field?.fieldName];
                                    if (!fieldData) return " ";

                                    // Handle options format (array of strings)
                                    if (Array.isArray(fieldData)) {
                                      return fieldData.join(", ");
                                    }

                                    // Handle checkboxes format (object with checkbox values)
                                    if (typeof fieldData === "object") {
                                      const selectedOptions = Object.entries(
                                        fieldData,
                                      )
                                        .filter(
                                          ([key, value]) => value === true,
                                        )
                                        .map(([key, value]) => {
                                          // Try to find the display name from field configuration
                                          const checkbox =
                                            field?.checkboxes?.find(
                                              (cb: any) => cb.fieldName === key,
                                            );
                                          return (
                                            checkbox?.displayName ||
                                            checkbox?.label ||
                                            key
                                          );
                                        });
                                      return selectedOptions.join(", ");
                                    }

                                    return fieldData.toString();
                                  })() || " ",
                                )}
                              </span>
                            )}
                            {field?.type === "pickList" && (
                              <span className="text-sm font-bold">
                                {renderInlineWithLinks(
                                  studentData?.[
                                    `${field?.fieldName}DisplayName`
                                  ] ||
                                    studentData?.[field?.fieldDisplayName] ||
                                    " ",
                                )}
                              </span>
                            )}
                            {field?.type === "date" && (
                              <span className="text-sm font-bold">
                                {": "}
                                {renderInlineWithLinks(
                                  formatDate(
                                    formatDateForDisplay(
                                      studentData?.[`${field?.fieldName}`],
                                    ),
                                    preferredDate,
                                  ) || " ",
                                )}
                              </span>
                            )}
                          </p>
                        )}
                      </div>
                    </div>
                  );
                }
                if (
                  field?.type === "canShowPreview" &&
                  checkVisibilityForPreview(field?.visibleWhen, studentData)
                ) {
                  return (
                    <div key={i} className={`mb-5 items-center`}>
                      <div className="prose dark:prose-invert max-w-none mb-6 text-sm">
                        <ReactMarkdown
                          className="markDown my-4"
                          components={{ a: LinkRenderer }}
                          remarkPlugins={[remarkGfm]}
                          rehypePlugins={[rehypeRaw]}
                        >
                          {replacePlaceholders(field?.text, studentData)}
                        </ReactMarkdown>
                      </div>
                    </div>
                  );
                }

                if (
                  field?.type === "subsection" &&
                  field?.sectionCanRepeat &&
                  checkVisibilityForPreview(field?.visibleWhen, studentData)
                ) {
                  return (
                    <div key={i}>
                      {studentData[field?.fieldName]?.map(
                        (item: any, index: number) => {
                          return (
                            <>
                              {field?.displayName && (
                                <>
                                  <p className="text-base font-bold mt-6 mb-2">
                                    {renderInlineWithLinks(
                                      `${field?.displayName} : ${index + 1}`,
                                    )}
                                  </p>
                                  {field?.customText && (
                                    <span className="text-xs text-black mb-2 block">
                                      {field.customText}
                                    </span>
                                  )}
                                </>
                              )}
                              {field?.[field?.subSection]?.fieldData?.map(
                                (fieldData: any, i: any) => {
                                  if (fieldData?.visibleWhen) {
                                    let isVisible = true;

                                    const conditions = Array.isArray(
                                      fieldData.visibleWhen,
                                    )
                                      ? fieldData.visibleWhen
                                      : [fieldData.visibleWhen];

                                    isVisible = conditions.some(
                                      (condition: any) => {
                                        const fieldValue =
                                          studentData?.[field?.fieldName]?.[
                                            index
                                          ]?.[condition?.fieldName];

                                        if (Array.isArray(condition.value)) {
                                          return condition.value.includes(
                                            fieldValue,
                                          );
                                        } else {
                                          return fieldValue === condition.value;
                                        }
                                      },
                                    );

                                    if (!isVisible) return null;
                                  }
                                  let nameLabel = fieldData?.displayName
                                    ?.replaceAll(
                                      "<firstName>",
                                      `${watchedFields["firstName"]} `,
                                    )
                                    ?.replaceAll(
                                      "<lastName>",
                                      ` ${watchedFields["lastName"]}`,
                                    );
                                  return (
                                    <div key={i} className="py-2">
                                      <div className="flex items-start gap-2">
                                        {(fieldData?.type === "document" ||
                                          fieldData?.type === "multiDocument" ||
                                          fieldData?.type === "checkbox" ||
                                          fieldData?.type === "signature") &&
                                          checkVisibilityForPreview(
                                            fieldData?.visibleWhen,
                                            studentData,
                                            field?.fieldName,
                                            index,
                                          ) && (
                                            <Image
                                              priority
                                              src={
                                                item[fieldData?.fieldName] ===
                                                  false ||
                                                item[fieldData?.fieldName] ===
                                                  undefined
                                                  ? uncheck
                                                  : check
                                              }
                                              height={16}
                                              width={16}
                                              alt="Follow us on Twitter"
                                            />
                                          )}
                                        <p className="text-sm">
                                          {renderInlineWithLinks(
                                            nameLabel ||
                                              fieldData?.label ||
                                              fieldData?.displayName ||
                                              "",
                                          )}
                                          {fieldData?.type !==
                                            "displayInformation" &&
                                            fieldData?.type !== "document" &&
                                            fieldData?.type !==
                                              "multiDocument" &&
                                            fieldData?.type !== "checkbox" && (
                                              <span className="text-sm font-bold">
                                                {": "}
                                                {renderInlineWithLinks(
                                                  getFieldDataValue(
                                                    fieldData,
                                                    item,
                                                  ) || "",
                                                )}
                                              </span>
                                            )}
                                        </p>
                                      </div>
                                    </div>
                                  );
                                },
                              )}
                            </>
                          );
                        },
                      )}
                    </div>
                  );
                }
                if (
                  field?.type === "subsection" &&
                  checkVisibilityForPreview(field?.visibleWhen, studentData)
                ) {
                  return (
                    <div key={i}>
                      <p className="text-sm font-bold">
                        {renderInlineWithLinks(
                          field?.displayName || field?.label || "",
                        )}
                      </p>
                      {field?.[field?.subSection]?.fieldData?.map(
                        (fieldData: any, i: any) => {
                          if (fieldData?.visibleWhen) {
                            let isVisible = true;

                            const conditions = Array.isArray(
                              fieldData.visibleWhen,
                            )
                              ? fieldData.visibleWhen
                              : [fieldData.visibleWhen];

                            isVisible = conditions.some((condition: any) => {
                              const fieldValue =
                                studentData?.[condition?.fieldName];

                              if (Array.isArray(condition.value)) {
                                return condition.value.includes(fieldValue);
                              } else {
                                return fieldValue === condition.value;
                              }
                            });

                            if (!isVisible) return null;
                          }
                          return (
                            checkVisibilityForPreview(
                              fieldData?.visibleWhen,
                              studentData,
                            ) && (
                              <div key={i} className="py-2">
                                <div className="flex items-start gap-2">
                                  {(fieldData?.type === "document" ||
                                    fieldData?.type === "multiDocument" ||
                                    fieldData?.type === "checkbox" ||
                                    fieldData?.type === "signature") && (
                                    <Image
                                      priority
                                      src={
                                        studentData[fieldData?.fieldName] ===
                                          false ||
                                        studentData[fieldData?.fieldName] ===
                                          undefined
                                          ? uncheck
                                          : check
                                      }
                                      height={16}
                                      width={16}
                                      alt="Follow us on Twitter"
                                    />
                                  )}

                                  <p className="text-sm">
                                    {renderInlineWithLinks(
                                      fieldData?.label ||
                                        fieldData?.displayName ||
                                        "",
                                    )}
                                    {fieldData?.type !== "displayInformation" &&
                                      fieldData?.type !== "document" &&
                                      fieldData?.type !== "checkbox" &&
                                      fieldData?.type !== "multiPickList" && (
                                        <span className="text-sm font-bold">
                                          {": "}
                                          {renderInlineWithLinks(
                                            getFieldDataValue(
                                              fieldData,
                                              studentData,
                                            ) || "",
                                          )}
                                        </span>
                                      )}
                                    {fieldData?.type === "multiPickList" && (
                                      <span className="text-sm font-bold">
                                        {": "}
                                        {renderInlineWithLinks(
                                          studentData?.[fieldData?.fieldName]
                                            ?.map((obj: any) => obj.value)
                                            .join(", ") || " ",
                                        )}
                                      </span>
                                    )}
                                  </p>
                                </div>
                              </div>
                            )
                          );
                        },
                      )}
                    </div>
                  );
                }
                return null;
              })}
            </div>
          );
        })}
      </div>
    );
  }
}
