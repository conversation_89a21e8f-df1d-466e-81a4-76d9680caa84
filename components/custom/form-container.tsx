import React from "react";
import ReactMarkdown from "react-markdown";
import DynamicFields from "./DynamicFields";
import { AlertBox } from "./alert-dialog";
import { <PERSON>Renderer } from "./linkRender";
import remarkGfm from "remark-gfm";
import rehypeRaw from "rehype-raw";

interface FormContainerProps {
  arrayIndex?: number;
  errors?: any;
  fieldData?: any;
  handleAddMore: (fieldItem: any) => void;
  handleRemove: (uniqueId: any, fieldItem: any) => void;
  watch?: Function;
  getLookupData?: Function;
  register?: any;
  setValue?: any;
  clearErrors?: any;
  uploadDocs?: Function;
  studentDetail?: any;
  handleDeleteFile?: Function;
  documents?: any;
  studentDetails: any;
  trigger?: any;
  setError?: any;
  subSectionArrayMap: { [key: string]: number[] };
  popupDetails?: any;
  formQuery?: any;
  enableAutoDetection?: boolean;
}

export default function FormContainer({
  documents,
  handleDeleteFile,
  studentDetail,
  setValue,
  arrayIndex = -1,
  errors,
  fieldData,
  watch = () => {},
  handleAddMore = () => {},
  getLookupData,
  register,
  clearErrors,
  uploadDocs,
  trigger,
  studentDetails,
  setError,
  handleRemove,
  subSectionArrayMap,
  popupDetails,
  formQuery,
  enableAutoDetection,
  ...rest
}: FormContainerProps) {
  const checkVisibility = (visibleWhenProps: any) => {
    if (!visibleWhenProps) {
      return true;
    }

    // Handle new "or" logic with multiple conditions
    if (visibleWhenProps.logic === "or" && Array.isArray(visibleWhenProps.conditions)) {
      return visibleWhenProps.conditions.some((condition: any) => {
        const fieldWatchValue = watch(condition.fieldName);

        if (typeof fieldWatchValue === "object" && fieldWatchValue !== null && !Array.isArray(fieldWatchValue)) {
          return fieldWatchValue?.value === condition.value || fieldWatchValue?.label === condition.value;
        }

        if (Array.isArray(fieldWatchValue)) {
          return fieldWatchValue.some(
            (v: any) => v?.value === condition.value || v?.label === condition.value || v === condition.value
          );
        }

        return fieldWatchValue === condition.value;
      });
    }

    const fieldWatchValue = watch(visibleWhenProps.fieldName);
    let isArr = Array.isArray(fieldWatchValue);
    const { condition, value } = visibleWhenProps;

    if (
      Array.isArray(visibleWhenProps?.value) &&
      condition !== "notEqual" &&
      condition !== "equal"
    ) {
      return visibleWhenProps?.value.some((item: string) => {
        if (typeof fieldWatchValue === "string") {
          return item === (fieldWatchValue as string);
        }

        if (typeof fieldWatchValue === "object" && fieldWatchValue !== null) {
          return (
            item === fieldWatchValue?.value || item === fieldWatchValue?.label
          );
        }

        if (Array.isArray(fieldWatchValue)) {
          return fieldWatchValue.some(
            (v: any) => item === v?.value || item === v?.label || item === v
          );
        }

        return false;
      });
    }
    if (condition === "exists") {
      return Array.isArray(fieldWatchValue)
        ? fieldWatchValue.length > 0
        : !!fieldWatchValue;
    }

    if (condition === "notExists") {
      return Array.isArray(fieldWatchValue)
        ? fieldWatchValue.length > 0
        : !fieldWatchValue;
    }

    if (typeof fieldWatchValue == "object" && !isArr) {
      if (condition === "notEqual") {
        return !value.some(
          (item: { value: string }) => item.value === fieldWatchValue?.value
        );
      }
      if (condition === "equal") {
        return value.some(
          (item: { value: string }) => item.value === fieldWatchValue?.value
        );
      }
      if (fieldWatchValue?.value == visibleWhenProps?.value) return true;
      return false;
    }
    if (visibleWhenProps?.value) {
      if (condition === "notEqual") {
        return !(
          visibleWhenProps?.value.some(
            (item: { value: string }) =>
              item.value === fieldWatchValue || item === fieldWatchValue
          ) ||
          (isArr &&
            fieldWatchValue.some((obj: any) =>
              value.some((item: { value: string }) => item.value === obj.value)
            )) ||
          (Array.isArray(visibleWhenProps?.value) &&
            visibleWhenProps?.value.includes(fieldWatchValue))
        );
      }
      if (condition === "equal") {
        return (
          visibleWhenProps?.value.some(
            (item: { value: string }) =>
              item.value === fieldWatchValue || item === fieldWatchValue
          ) ||
          (isArr &&
            fieldWatchValue.some((obj: any) =>
              value.some((item: { value: string }) => item.value === obj.value)
            ))
        );
      }
      // Handle array case (multiCheckbox with options format)
      if (isArr) {
        const result = fieldWatchValue.some(
          (obj: any) =>
            obj.value === visibleWhenProps?.value ||
            obj === visibleWhenProps?.value,
        );

        return result;
      }

      // Handle single value case
      const result = fieldWatchValue === visibleWhenProps?.value;

      return result;
    }
    return true;
  };

  return (
    <div className="mt-5 ml-1 mr-1">
      {fieldData?.map((fieldItem: any, fieldIndex: number) => {
        if (fieldItem?.sectionCanRepeat) {
          const fieldArray = subSectionArrayMap[fieldItem.fieldName] || [];
          return (
            <div key={fieldIndex}>
              {fieldArray.map((subSectionIndex: number, subIndex: number) => (
                <div key={subSectionIndex}>
                  <DynamicFields
                    subSectionIndex={subSectionIndex}
                    subIndex={subIndex}
                    getLookupData={getLookupData}
                    register={register}
                    arrayIndex={arrayIndex}
                    selectedValue={
                      watch(fieldItem?.fieldName) ||
                      watch(`${fieldItem?.documentType}`) ||
                      ""
                    }
                    isVisibleWhen={checkVisibility(fieldItem?.visibleWhen)}
                    fieldItem={fieldItem}
                    studentData={studentDetails}
                    label={
                      fieldItem?.label ||
                      fieldItem?.displayName ||
                      fieldItem?.placeholder
                    }
                    handleValueChanged={(value: any, type?: string) => {
                      if (!fieldItem?.fieldName) return;
                      clearErrors(fieldItem?.fieldName);
                      clearErrors(`${fieldItem?.documentType}`);
                      if (type === "file") {
                        setValue(fieldItem.fieldName, value);
                        return;
                      }
                      if (
                        fieldItem?.type === "pickList" &&
                        fieldItem?.fieldDisplayName
                      ) {
                        setValue(
                          fieldItem?.fieldDisplayName,
                          typeof value === "object" ? value.label : value
                        );
                      }
                      if (fieldItem?.resetChild) {
                        if (Array.isArray(fieldItem?.resetChild)) {
                          for (const resetItem of fieldItem?.resetChild) {
                            setValue(
                              resetItem,
                              value?.[fieldItem?.dependentFieldName] || ""
                            );
                            clearErrors(resetItem);
                          }
                        } else {
                          setValue(
                            fieldItem?.resetChild,
                            value?.[fieldItem?.dependentFieldName] || ""
                          );
                          clearErrors(fieldItem?.resetChild);
                        }
                      }
                      setValue(fieldItem?.fieldName, value);
                    }}
                    errorMessage={
                      errors?.[fieldItem?.fieldName]?.message ||
                      errors?.[`${fieldItem?.documentType}`]?.message
                    }
                    name={fieldItem?.fieldName}
                    uploadDocs={uploadDocs}
                    handleDeleteFile={handleDeleteFile}
                    trigger={trigger}
                    watch={watch}
                    clearErrors={clearErrors}
                    setValue={setValue}
                    setError={setError}
                    handleRemove={handleRemove}
                    {...rest}
                    enableAutoDetection={enableAutoDetection}
                  />
                </div>
              ))}
              {errors[fieldItem.requiredWhenFieldName] && (
                <span className="text-sm text-[red] h-0">
                  {errors[fieldItem.requiredWhenFieldName].message}
                </span>
              )}
              {fieldArray.length < fieldItem?.maxLength &&
                checkVisibility(fieldItem?.addOnButtonDetails?.visibleWhen) && (
                  <div
                    className="text-background rounded  bg-slate-900/90  hover:bg-primary font-bold  text-sm px-5 py-2.5 mt-5 me-2 mb-5 cursor-pointer text-center"
                    onClick={() => {
                      handleAddMore(fieldItem);
                    }}
                  >
                    {fieldItem?.addOnButtonDetails?.label || `Add ${fieldItem.displayName}`}
                  </div>
                )}
            </div>
          );
        }

        return (
          <div key={fieldIndex}>
            <DynamicFields
              getLookupData={getLookupData}
              register={register}
              arrayIndex={arrayIndex}
              selectedValue={
                watch(fieldItem?.fieldName) ||
                watch(`${fieldItem?.documentType}`) ||
                ""
              }
              isVisibleWhen={checkVisibility(fieldItem?.visibleWhen)}
              fieldItem={fieldItem}
              studentData={studentDetails}
              label={
                fieldItem?.label ||
                fieldItem?.displayName ||
                fieldItem?.placeholder
              }
              handleValueChanged={(value: any, type?: string) => {
                if (!fieldItem?.fieldName) return;
                clearErrors(fieldItem?.fieldName);
                clearErrors(`${fieldItem?.documentType}`);
                if (type === "file") {
                  setValue(fieldItem.fieldName, value);
                  return;
                }
                if (fieldItem.fieldName === "addMore") {
                  if (
                    fieldItem.type === "radio" &&
                    value?.toLoweCase() === "yes"
                  ) {
                    handleAddMore(fieldItem);
                  }
                }

                if (
                  fieldItem.type === "pickList" &&
                  fieldItem?.fieldDisplayName
                ) {
                  setValue(
                    fieldItem?.fieldDisplayName,
                    typeof value === "object" ? value.label : value
                  );
                  if (fieldItem?.mapProductId) {
                    setValue(
                      fieldItem?.mapProductId,
                      value?.[fieldItem?.mapProductId]
                    );
                  }
                }
                if (fieldItem?.resetChild) {
                  if (Array.isArray(fieldItem?.resetChild)) {
                    for (const resetItem of fieldItem?.resetChild) {
                      setValue(
                        resetItem,
                        value?.[fieldItem?.dependentFieldName] || ""
                      );
                      clearErrors(resetItem);
                    }
                  } else {
                    setValue(
                      fieldItem?.resetChild,
                      value?.[fieldItem?.dependentFieldName] || ""
                    );
                    clearErrors(fieldItem?.resetChild);
                  }
                }
                if (fieldItem.subFieldName) {
                  setValue(fieldItem?.subFieldName, value);
                }
                if (fieldItem?.resetWithCondition) {
                  const { condition, dependentFieldName } =
                    fieldItem.resetWithCondition;

                  if (value === condition.value) {
                    const dependentFieldValues = watch(dependentFieldName);
                    if (typeof dependentFieldValues === "object") {
                      for (const key in dependentFieldValues) {
                        setValue(key, dependentFieldValues[key]);
                        clearErrors(key);
                      }
                    }
                  }
                }
                setValue(fieldItem?.fieldName, value);
              }}
              errorMessage={
                errors?.[fieldItem?.fieldName]?.message ||
                errors?.[`${fieldItem?.documentType}`]?.message
              }
              name={fieldItem?.fieldName}
              uploadDocs={uploadDocs}
              handleDeleteFile={handleDeleteFile}
              trigger={trigger}
              watch={watch}
              clearErrors={clearErrors}
              setError={setError}
              setValue={setValue}
              isOcrReprocess={formQuery.isOcrReprocess}
              maxOcrReprocessCount={formQuery.maxOcrReprocess}
              {...rest}
              enableAutoDetection={enableAutoDetection}
            />
          </div>
        );
      })}
      {popupDetails && (
        <AlertBox
          title={popupDetails?.title}
          placeholder={popupDetails?.placeholder}
        >
          <div className="prose dark:prose-invert max-w-none mb-6 text-sm">
            <ReactMarkdown
              className="markDown my-4"
              components={{ a: LinkRenderer }}
              remarkPlugins={[remarkGfm]}
              rehypePlugins={[rehypeRaw]}
            >
              {popupDetails?.text}
            </ReactMarkdown>
          </div>
        </AlertBox>
      )}
    </div>
  );
}
